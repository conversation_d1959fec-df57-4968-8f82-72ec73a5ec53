<?php
require_once 'config.php';

header('Content-Type: text/plain');

$link = get_db_connection();

echo "=== Debug Online Users ===\n";
echo "Current time: " . date('Y-m-d H:i:s') . "\n\n";

// Check current users data
$result = mysqli_query($link, "SELECT username, last_seen, last_login, TIMESTAMPDIFF(MINUTE, last_seen, NOW()) as minutes_ago FROM users ORDER BY last_seen DESC");

echo "Current users data:\n";
while($row = mysqli_fetch_assoc($result)) {
    $minutes_ago = $row['minutes_ago'] !== null ? $row['minutes_ago'] . ' minutes ago' : 'N/A';
    echo "User: {$row['username']}, Last Seen: {$row['last_seen']} ({$minutes_ago}), Last Login: {$row['last_login']}\n";
}

echo "\n";

// Check current online count using old logic (all users with last_seen not null)
$result2 = mysqli_query($link, "SELECT COUNT(id) AS online_count FROM users WHERE last_seen IS NOT NULL");
$row2 = mysqli_fetch_assoc($result2);
echo "Online count (old logic - all with last_seen): {$row2['online_count']}\n";

// Check online count with new time-based logic (within last 5 minutes)
$result3 = mysqli_query($link, "SELECT COUNT(id) AS online_count FROM users WHERE last_seen IS NOT NULL AND last_seen >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)");
$row3 = mysqli_fetch_assoc($result3);
echo "Online count (new logic - within 5 minutes): {$row3['online_count']}\n";

// Show users who are considered online with new logic
$result4 = mysqli_query($link, "SELECT username, last_seen, TIMESTAMPDIFF(MINUTE, last_seen, NOW()) as minutes_ago FROM users WHERE last_seen IS NOT NULL AND last_seen >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) ORDER BY last_seen DESC");
echo "\nUsers considered online (within 5 minutes):\n";
while($row = mysqli_fetch_assoc($result4)) {
    echo "- {$row['username']} (last seen {$row['minutes_ago']} minutes ago)\n";
}

close_db_connection($link);
