<?php
session_start();
require_once 'config.php';
require_once 'credit_system.php';

// Check if user is logged in
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    echo json_encode(['success' => false, 'message' => 'Access denied. You must be logged in.']);
    exit;
}

$user_id = $_SESSION["id"];
$action = $_POST['action'] ?? '';

header('Content-Type: application/json');

switch ($action) {
    case 'transfer_to_kms':
        transferCommissionToKMS($user_id);
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

/**
 * Transfer commission balance to KMS Credit wallet
 */
function transferCommissionToKMS($user_id) {
    global $link;
    
    $amount = floatval($_POST['amount'] ?? 0);
    
    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid transfer amount']);
        return;
    }
    
    mysqli_begin_transaction($link);
    
    try {
        // Get user's current commission balance
        $sql = "SELECT commission_balance FROM user_wallets WHERE user_id = ?";
        $stmt = execute_query($link, $sql, "i", [$user_id]);
        
        if (!$stmt) {
            throw new Exception('Failed to get wallet information');
        }
        
        $result = mysqli_stmt_get_result($stmt);
        $wallet = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$wallet) {
            throw new Exception('Wallet not found');
        }
        
        $current_commission = floatval($wallet['commission_balance']);
        
        if ($amount > $current_commission) {
            throw new Exception('Transfer amount exceeds available commission balance');
        }
        
        // Initialize credit system
        $credit_system = new KMSCreditSystem($link);
        
        // Add amount to KMS Credit balance
        $add_result = $credit_system->addCredit(
            $user_id,
            $amount,
            'affiliate_transfer',
            "Commission transferred to KMS Credit",
            "AFFILIATE_TRANSFER_" . time()
        );
        
        if (!$add_result['success']) {
            throw new Exception($add_result['message']);
        }
        
        // Deduct from commission balance
        $update_sql = "UPDATE user_wallets SET commission_balance = commission_balance - ? WHERE user_id = ?";
        $update_stmt = execute_query($link, $update_sql, "di", [$amount, $user_id]);
        
        if (!$update_stmt) {
            throw new Exception('Failed to update commission balance');
        }
        mysqli_stmt_close($update_stmt);
        
        // Log the transfer
        $log_sql = "INSERT INTO affiliate_transfers (user_id, amount, transfer_type, status, created_at) 
                    VALUES (?, ?, 'commission_to_kms', 'completed', NOW())";
        $log_stmt = execute_query($link, $log_sql, "id", [$user_id, $amount]);
        
        if ($log_stmt) {
            mysqli_stmt_close($log_stmt);
        }
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Commission transferred successfully to KMS Credit',
            'transferred_amount' => $amount,
            'transaction_id' => $add_result['transaction_id']
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}
?>
