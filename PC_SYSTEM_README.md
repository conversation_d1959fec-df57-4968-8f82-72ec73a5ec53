# 💻 PC Components Management System

## 🎉 功能概述

這是一個完整的電腦組裝和銷售管理系統，包含以下主要功能：

### ✅ 已實現功能

1. **首頁電腦區域增強**
   - 五大賣點展示（零件品質、外觀燈光、客製化、三年保固、系統優化）
   - 多語言支持（中英文）
   - 視覺化設計與配色

2. **電腦零件管理系統**
   - 零件分類管理（CPU、GPU、RAM、儲存裝置等）
   - 零件詳細信息（品牌、型號、規格、價格、庫存）
   - JSON格式規格存儲
   - 完整的CRUD操作

3. **Pre-built電腦配置**
   - 預設6種不同等級配置
   - 動態價格和折扣管理
   - 規格摘要展示
   - 適用場景分類（遊戲、影片剪輯、通用）

4. **會員電腦組裝功能**
   - 三種配置模式：簡單、詳細、Pre-built
   - 實時價格計算
   - 配置摘要顯示
   - 訂單提交系統

5. **管理員後台**
   - 統一的配色設計
   - 零件庫存管理
   - Pre-built配置管理
   - 訂單狀態管理
   - 統計數據展示

## 🚀 安裝步驟

### 1. 數據庫設置
```
訪問：/PHP/setup_pc_system.php
```
這將創建所有必要的數據庫表並插入示例數據。

### 2. 測試系統
```
訪問：/PHP/test_pc_system.php
```
驗證所有組件是否正常工作。

### 3. 開始使用
- **管理員**：訪問 `/PHP/admin.php` → 點擊 "💻 PC Components & Configs"
- **會員**：訪問 `/PHP/member.php` → 查看 "💻 電腦組裝配置器" 部分

## 📁 文件結構

### 核心文件
- `PHP/pc_components_api.php` - 主要API接口
- `PHP/pc_components_api_extended.php` - 擴展API功能
- `PHP/admin_pc_management.php` - 管理員界面
- `PHP/setup_pc_system.php` - 系統安裝腳本

### 數據庫文件
- `SQL/setup_pc_components.sql` - 數據庫表結構
- `SQL/insert_sample_prebuilt_configs.sql` - 示例配置數據

### 語言文件
- `lang/en.json` - 英文翻譯
- `lang/zh-CN.json` - 中文翻譯

## 🗄️ 數據庫結構

### 主要表格
1. **pc_component_categories** - 零件分類
2. **pc_components** - 零件詳細信息
3. **pc_prebuilt_configs** - Pre-built配置
4. **pc_orders** - PC訂單

### 關鍵字段
- JSON格式的規格存儲
- 多語言支持字段
- 價格和折扣管理
- 庫存追蹤

## 🎨 界面設計

### 配色方案
- 主背景：`#a48f19`（金棕色）
- 容器背景：`rgb(5 195 182)`（青綠色）
- 按鈕顏色：`rgb(253, 202, 0)`（金黃色）
- 文字顏色：白色系

### 響應式設計
- 支持桌面和移動設備
- 彈性網格佈局
- 適應性按鈕排列

## 🔧 API接口

### 主要端點
- `GET /pc_components_api.php?action=get_categories` - 獲取分類
- `GET /pc_components_api.php?action=get_components` - 獲取零件
- `GET /pc_components_api.php?action=get_prebuilt_configs` - 獲取配置
- `POST /pc_components_api.php` - CRUD操作

### 權限控制
- 管理員：完整CRUD權限
- 會員：查看和訂單提交權限
- 訪客：僅查看權限

## 📊 管理功能

### 零件管理
- 添加/編輯/刪除零件
- 庫存管理
- 價格調整
- 規格編輯

### 配置管理
- Pre-built套餐管理
- 價格和折扣設置
- 規格摘要編輯
- 啟用/停用控制

### 訂單管理
- 訂單狀態追蹤
- 價格調整
- 客戶信息查看
- 配置詳情檢視

## 🛠️ 自定義選項

### 管理員限制
- 可限制會員使用特定配置模式
- 動態控制可用功能
- 價格調整權限

### 擴展性
- 易於添加新的零件分類
- 支持自定義規格字段
- 可擴展的訂單狀態

## 🔍 故障排除

### 常見問題
1. **API返回錯誤**：檢查數據庫連接和表結構
2. **權限問題**：確認用戶登錄狀態和管理員權限
3. **數據不顯示**：運行測試腳本檢查系統狀態

### 調試工具
- `/PHP/test_pc_system.php` - 系統測試
- 瀏覽器開發者工具 - 檢查API調用
- 數據庫查詢日誌 - 檢查SQL執行

## 📞 支持

如需技術支持或功能擴展，請聯繫系統管理員。

---

**版本**: 1.0  
**最後更新**: 2024年12月  
**兼容性**: PHP 7.4+, MySQL 5.7+
