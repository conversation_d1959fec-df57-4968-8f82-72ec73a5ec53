<?php
session_start();
require_once 'config.php';

// Check if user is admin
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    header("location: ../index.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Affiliate Commission Review</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #a48f19;
            color: white;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: auto;
            background-color: #2b9869;
            padding: 20px;
            border-radius: 14px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.5);
        }
        h1, h2 {
            color: #00ffff;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
        .commission-card {
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #ffd700;
        }
        .commission-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .commission-amount {
            font-size: 24px;
            font-weight: bold;
            color: #00ff00;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        }
        .commission-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .detail-item {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
        }
        .detail-label {
            color: #00ffff;
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
        }
        .detail-value {
            color: white;
            margin-top: 5px;
        }
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        .btn-approve {
            background-color: #4CAF50;
            color: white;
        }
        .btn-reject {
            background-color: #f44336;
            color: white;
        }
        .btn-back {
            background-color: #2196F3;
            color: white;
            margin-bottom: 20px;
        }
        .notes-input {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #555;
            background: #333;
            color: white;
            margin-top: 10px;
            resize: vertical;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #ccc;
        }
        .no-commissions {
            text-align: center;
            padding: 40px;
            color: #ccc;
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <button class="btn btn-back" onclick="window.location.href='admin_credit_dashboard.php'">← Back to Admin Dashboard</button>
        
        <h1>Affiliate Commission Review</h1>
        <p style="text-align: center; color: #ccc;">Review and approve/reject pending affiliate commissions</p>
        
        <div id="commissions-container">
            <div class="loading">Loading pending commissions...</div>
        </div>
    </div>

    <script>
        // Load pending commissions on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadPendingCommissions();
        });

        function loadPendingCommissions() {
            fetch('admin_affiliate_commissions.php?action=get_pending_commissions')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayCommissions(data.commissions);
                    } else {
                        document.getElementById('commissions-container').innerHTML = 
                            '<div class="no-commissions">Error loading commissions</div>';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('commissions-container').innerHTML = 
                        '<div class="no-commissions">Error loading commissions</div>';
                });
        }

        function displayCommissions(commissions) {
            const container = document.getElementById('commissions-container');
            
            if (commissions.length === 0) {
                container.innerHTML = '<div class="no-commissions">No pending commissions to review</div>';
                return;
            }

            container.innerHTML = commissions.map(commission => `
                <div class="commission-card" id="commission-${commission.id}">
                    <div class="commission-header">
                        <div>
                            <h3 style="margin: 0; color: #ffd700;">Commission #${commission.id}</h3>
                            <div style="font-size: 12px; color: #ccc;">Created: ${commission.created_at}</div>
                        </div>
                        <div class="commission-amount">$${parseFloat(commission.commission_amount).toFixed(2)}</div>
                    </div>
                    
                    <div class="commission-details">
                        <div class="detail-item">
                            <div class="detail-label">Referrer</div>
                            <div class="detail-value">${commission.referrer_username}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Referred User</div>
                            <div class="detail-value">${commission.referred_username}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Order Number</div>
                            <div class="detail-value">#${commission.order_number}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Order Date</div>
                            <div class="detail-value">${commission.order_date}</div>
                        </div>
                    </div>
                    
                    <div>
                        <textarea class="notes-input" id="notes-${commission.id}" placeholder="Admin notes (optional)"></textarea>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="btn btn-approve" onclick="approveCommission(${commission.id})">
                            ✓ Approve Commission
                        </button>
                        <button class="btn btn-reject" onclick="rejectCommission(${commission.id})">
                            ✗ Reject Commission
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function approveCommission(commissionId) {
            const notes = document.getElementById(`notes-${commissionId}`).value;
            
            if (!confirm('Are you sure you want to approve this commission?')) {
                return;
            }

            fetch('admin_affiliate_commissions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'approve_commission',
                    commission_id: commissionId,
                    admin_notes: notes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Commission approved successfully!');
                    document.getElementById(`commission-${commissionId}`).remove();
                    
                    // Check if no more commissions
                    if (document.querySelectorAll('.commission-card').length === 0) {
                        document.getElementById('commissions-container').innerHTML = 
                            '<div class="no-commissions">No pending commissions to review</div>';
                    }
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while approving the commission');
            });
        }

        function rejectCommission(commissionId) {
            const notes = document.getElementById(`notes-${commissionId}`).value;
            
            if (!notes.trim()) {
                alert('Please provide a reason for rejecting this commission');
                return;
            }
            
            if (!confirm('Are you sure you want to reject this commission?')) {
                return;
            }

            fetch('admin_affiliate_commissions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'reject_commission',
                    commission_id: commissionId,
                    admin_notes: notes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Commission rejected successfully!');
                    document.getElementById(`commission-${commissionId}`).remove();
                    
                    // Check if no more commissions
                    if (document.querySelectorAll('.commission-card').length === 0) {
                        document.getElementById('commissions-container').innerHTML = 
                            '<div class="no-commissions">No pending commissions to review</div>';
                    }
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while rejecting the commission');
            });
        }
    </script>
</body>
</html>
