<?php
// Simple session start with error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Start session
session_start();

// Include required files
require_once 'config.php';
require_once 'language.php';

// Debug session data
error_log('Member Page - Simple session data: ' . print_r([
    'session_id' => session_id(),
    'session_name' => session_name(),
    'loggedin' => $_SESSION['loggedin'] ?? 'not set',
    'user_id' => $_SESSION['id'] ?? 'not set'
], true));

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    error_log('Redirecting to login: User not logged in. Session data: ' . print_r($_SESSION, true));
    header('Location: ../index.php');
    exit;
}

// Store session ID for JavaScript
echo "<script>const SESSION_ID = '" . session_id() . "';</script>";
?>
<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= t('member_page_title') ?></title>
    <link rel="stylesheet" href="../CSS/custom-modal.css" />
    <style>
        body { font-family: Arial, sans-serif; background-color: #a48f19; color: white; margin: 0; padding: 20px; }
        .container { max-width: 1000px; margin: auto; background-color: #2b9869; padding: 20px; border-radius: 14px; box-shadow: 0 5px 15px rgba(0,0,0,0.5); }
        h1, h2 { color: #00ffff; text-align: center; text-shadow: 2px 2px 4px rgba(0,0,0,0.8);}
        h3, h4, h5, h6 { text-shadow: 1px 1px 2px rgba(0,0,0,0.8); }
        p, div, span { text-shadow: 1px 1px 2px rgba(0,0,0,0.6); }
        #welcome-msg { text-align: center; font-size: 24px; margin-bottom: 20px; text-shadow: 2px 2px 4px rgba(0,0,0,0.8); }
        .wallet-section { margin: 20px 0; }
        .wallet-card { background: rgba(19,73,240,0.4); padding: 10px; border-radius: 10px; margin-bottom: 20px; }
        .wallet-balance { text-align: center; margin-bottom: 20px; }
        .balance-amount { font-size: 36px; font-weight: bold; color: #ffd700; margin: 10px 0; }
        .wallet-stats { display: flex; justify-content: space-around; margin: 20px 0; flex-wrap: wrap; gap: 15px; }
        .stat-item { text-align: center; flex: 1; min-width: 150px; }
        .stat-item h4 { color: #00ffff; margin: 0 0 10px 0; font-size: 14px; text-shadow: 1px 1px 2px rgba(0,0,0,0.8); }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #ffd700;
            background: linear-gradient(145deg, rgba(255,215,0,0.1), rgba(255,215,0,0.05));
            border: 2px solid rgba(255,215,0,0.3);
            border-radius: 10px;
            padding: 15px 10px;
            margin: 5px 0;
            box-shadow:
                0 4px 8px rgba(0,0,0,0.3),
                inset 0 1px 0 rgba(255,255,255,0.1),
                inset 0 -1px 0 rgba(0,0,0,0.2);
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .stat-value::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }
        .stat-value:hover {
            transform: translateY(-2px);
            box-shadow:
                0 6px 12px rgba(0,0,0,0.4),
                inset 0 1px 0 rgba(255,255,255,0.2),
                inset 0 -1px 0 rgba(0,0,0,0.3);
        }
        .stat-value:hover::before {
            left: 100%;
        }
        .wallet-actions { display: flex; justify-content: center; gap: 15px; margin-top: 20px; }
        button {
            cursor: pointer;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.4);
        }
        .deposit-btn { background-color: #4CAF50; color: white; }
        .transfer-btn { background-color: #2196F3; color: white; }
        .history-btn { background-color: #9C27B0; color: white; }
        .btn-info { background-color: #00bcd4; color: white; }
        .btn-success { background-color: #4CAF50; color: white; }
        .btn-primary { background-color: #007bff; color: white; }

        /* Service Prices Styles */
        .service-prices-container { padding: 20px; }
        .service-category { margin-bottom: 30px; }
        .service-category h3 { color: #00ffff; margin-bottom: 15px; }
        .prices-list { background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; margin-bottom: 15px; }
        .price-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .price-item:last-child { border-bottom: none; }
        .price-name { flex: 1; color: white; }
        .price-value { color: #00ff00; font-weight: bold; }
        .price-unit { color: #ccc; font-size: 0.9em; margin-left: 5px; }
        .service-actions {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        /* Order Modal Styles */
        .order-modal-content { padding: 20px; max-width: 600px; }
        .services-list { max-height: 400px; overflow-y: auto; margin: 20px 0; }
        .service-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            margin-bottom: 10px;
            background: rgba(0,0,0,0.3);
        }
        .service-info { flex: 1; }
        .service-name { color: white; font-weight: bold; margin-bottom: 5px; }
        .service-price { color: #00ff00; }
        .quantity-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .quantity-control button {
            width: 30px;
            height: 30px;
            border: none;
            background: #007bff;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        }
        .quantity-control input {
            width: 60px;
            text-align: center;
            padding: 5px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #222;
            color: white;
        }
        .order-summary {
            background: rgba(0,0,0,0.5);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .summary-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            color: white;
        }
        .summary-line.total {
            font-weight: bold;
            font-size: 1.2em;
            border-top: 1px solid rgba(255,255,255,0.3);
            padding-top: 10px;
            color: #00ff00;
        }
        .order-options { margin: 20px 0; }
        .order-options label {
            display: block;
            color: white;
            margin-bottom: 10px;
        }
        .order-options textarea {
            width: 100%;
            height: 80px;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #222;
            color: white;
            resize: vertical;
        }
        .order-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <!-- Top right buttons -->
    <div style="position: fixed; top: 20px; right: 20px; display: flex; gap: 10px;">
        <button class="account-settings-btn" id="accountSettingsBtn" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer;">
            ⚙️ Account Settings
        </button>
        <button class="logout-btn" id="logoutBtn" style="padding: 10px 20px; background: #f44336; color: white; border: none; border-radius: 5px; cursor: pointer;">
            <?= t('logout_button') ?>
        </button>
    </div>

    <div class="container">
        <h1 id="welcome-msg"><?= str_replace('{username}', htmlspecialchars($_SESSION["username"]), t('welcome_message')) ?></h1>

        <!-- KMS Credit Wallet Section -->
        <div class="wallet-section">
            <h2>💰 KMS Credit Wallet</h2>
            <div class="wallet-card">
                <div class="wallet-balance">
                    <h3>Available Balance</h3>
                    <div class="balance-amount" id="walletBalance">$0.00</div>
                </div>

                <div class="wallet-stats">
                    <div class="stat-item">
                        <h4>Total Deposited</h4>
                        <div class="stat-value" id="totalDeposited">$0.00</div>
                    </div>
                    <div class="stat-item">
                        <h4>Total Spent</h4>
                        <div class="stat-value" id="totalSpent">$0.00</div>
                    </div>
                    <div class="stat-item">
                        <h4>Frozen Balance</h4>
                        <div class="stat-value" id="frozenBalance">$0.00</div>
                    </div>
                </div>

                <div class="wallet-actions">
                    <button class="deposit-btn" onclick="openDepositModal()">💳 Deposit</button>
                    <button class="transfer-btn" onclick="openTransferModal()">💸 Transfer</button>
                    <button class="history-btn" onclick="openTransactionHistory()">📊 History</button>
                </div>
            </div>
        </div>

        <!-- Affiliate Section -->
        <div class="wallet-section">
            <h2>🤝 Affiliate Program</h2>
            <div class="wallet-card">
                <div class="wallet-balance">
                    <h3>Your Referral Code</h3>
                    <div style="font-size: 24px; color: #00ff00; font-weight: bold; margin: 10px 0;" id="affiliateCode">Loading...</div>
                    <div style="font-size: 14px; color: #ccc;">Share this code to earn $50 per successful referral!</div>
                </div>

                <div class="wallet-stats">
                    <div class="stat-item">
                        <h4>Total Referrals</h4>
                        <div class="stat-value" id="totalReferrals">0</div>
                    </div>
                    <div class="stat-item">
                        <h4>Commission Earned</h4>
                        <div class="stat-value" id="totalCommissions">$0.00</div>
                    </div>
                    <div class="stat-item">
                        <h4>Available to Withdraw</h4>
                        <div class="stat-value" id="commissionBalance">$0.00</div>
                    </div>
                </div>

                <div class="wallet-actions">
                    <button class="btn-info" onclick="openShareModal()">📤 Share Link</button>
                    <button class="btn-primary" onclick="openTransferToKMSModal()">💳 Transfer to KMS Credit</button>
                    <button class="btn-success" onclick="openWithdrawModal()">💰 Withdraw</button>
                    <button class="history-btn" onclick="openAffiliateHistory()">📊 Referral History</button>
                </div>
            </div>
        </div>

        <!-- PC Builder Section -->
        <div class="wallet-section">
            <h2><?= t('pc_builder_title') ?></h2>
            <div class="wallet-card">
                <div class="pc-builder-container">
                    <!-- Mode Selection -->
                    <div class="mode-selection" style="margin-bottom: 30px;">
                        <h3 style="text-align: center; color: #00ffff; margin-bottom: 20px;"><?= t('pc_builder_mode_selection') ?></h3>
                        <div class="mode-options" style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                            <div class="mode-option" style="flex: 1; min-width: 200px; max-width: 300px; padding: 20px; background: rgba(0,0,0,0.3); border-radius: 10px; border: 2px solid transparent; cursor: pointer; text-align: center;" onclick="selectPCMode('simple')">
                                <h4 style="color: #00ff00; margin-bottom: 10px;"><?= t('pc_builder_simple_mode') ?></h4>
                                <p style="font-size: 14px; color: #ccc;"><?= t('pc_builder_simple_desc') ?></p>
                            </div>
                            <div class="mode-option" style="flex: 1; min-width: 200px; max-width: 300px; padding: 20px; background: rgba(0,0,0,0.3); border-radius: 10px; border: 2px solid transparent; cursor: pointer; text-align: center;" onclick="selectPCMode('detailed')">
                                <h4 style="color: #4ecdc4; margin-bottom: 10px;"><?= t('pc_builder_detailed_mode') ?></h4>
                                <p style="font-size: 14px; color: #ccc;"><?= t('pc_builder_detailed_desc') ?></p>
                            </div>
                            <div class="mode-option" style="flex: 1; min-width: 200px; max-width: 300px; padding: 20px; background: rgba(0,0,0,0.3); border-radius: 10px; border: 2px solid transparent; cursor: pointer; text-align: center;" onclick="selectPCMode('prebuilt')">
                                <h4 style="color: #ffd700; margin-bottom: 10px;"><?= t('pc_builder_prebuilt_mode') ?></h4>
                                <p style="font-size: 14px; color: #ccc;"><?= t('pc_builder_prebuilt_desc') ?></p>
                            </div>
                        </div>

                        <!-- Admin Restriction Info -->
                        <div id="admin-restriction-info" style="margin-top: 20px; padding: 15px; background: rgba(255,165,0,0.1); border-radius: 10px; border-left: 4px solid #ffa500; display: none;">
                            <strong><?= t('pc_builder_admin_restriction') ?></strong>
                            <span id="restriction-text"><?= t('pc_builder_restriction_none') ?></span>
                        </div>
                    </div>

                    <!-- Configuration Areas -->
                    <div id="simple-mode-config" class="config-area" style="display: none;">
                        <h3 style="color: #00ff00; text-align: center; margin-bottom: 20px;"><?= t('pc_builder_simple_mode') ?></h3>
                        <div class="simple-config-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                            <!-- Simple mode configuration will be loaded here -->
                        </div>
                    </div>

                    <div id="detailed-mode-config" class="config-area" style="display: none;">
                        <h3 style="color: #4ecdc4; text-align: center; margin-bottom: 20px;"><?= t('pc_builder_detailed_mode') ?></h3>
                        <div class="detailed-config-container">
                            <!-- Detailed component selection will be loaded here -->
                        </div>
                    </div>

                    <div id="prebuilt-mode-config" class="config-area" style="display: none;">
                        <h3 style="color: #ffd700; text-align: center; margin-bottom: 20px;"><?= t('pc_builder_prebuilt_mode') ?></h3>
                        <div class="prebuilt-configs-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px;">
                            <!-- Pre-built configurations will be loaded here -->
                        </div>
                    </div>

                    <!-- Configuration Summary -->
                    <div id="config-summary" style="display: none; margin-top: 30px; padding: 20px; background: rgba(0,255,255,0.1); border-radius: 10px; border: 2px solid #00ffff;">
                        <h3 style="color: #00ffff; text-align: center; margin-bottom: 20px;">Configuration Summary</h3>
                        <div id="summary-content"></div>
                        <div class="summary-total" style="text-align: center; margin-top: 20px;">
                            <h3 style="color: #ffd700;"><?= t('pc_builder_estimated_total') ?>: <span id="estimated-total">$0.00</span></h3>
                            <button class="btn-primary" onclick="requestPCQuote()" style="padding: 15px 30px; font-size: 18px; margin-top: 10px;"><?= t('pc_builder_request_quote') ?></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Service Prices Section -->
        <div class="wallet-section">
            <h2>💰 Service Prices</h2>
            <div class="wallet-card">
                <div class="service-prices-container">
                    <div class="service-category">
                        <h3>📸 Optimize Photo & Video Services</h3>
                        <div id="optimizePrices" class="prices-list">
                            <div style="text-align: center; padding: 20px;">
                                <div style="font-size: 18px; color: #ccc;">Loading prices...</div>
                            </div>
                        </div>
                    </div>

                    <div class="service-category">
                        <h3>🖨️ Print Services</h3>
                        <div id="printPrices" class="prices-list">
                            <div style="text-align: center; padding: 20px;">
                                <div style="font-size: 18px; color: #ccc;">Loading prices...</div>
                            </div>
                        </div>
                    </div>

                    <div class="service-actions">
                        <button class="btn-primary" onclick="openOrderModal('optimize')">📸 Order Optimize Services</button>
                        <button class="btn-primary" onclick="openOrderModal('print')">🖨️ Order Print Services</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order History Section -->
        <div class="wallet-section">
            <h2>📋 Order History</h2>
            <div class="wallet-card">
                <div id="ordersList" style="max-height: 400px; overflow-y: auto;">
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 18px; color: #ccc;">Loading orders...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const i18n = <?= json_encode($translations, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE) ?>;
            
            // Load service prices
            function loadServicePrices() {
                console.log('Loading service prices...');

                // Load optimize prices
                fetch('service_prices_api.php?action=get_prices&category=optimize&active_only=true')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayPrices('optimizePrices', data.prices);
                        } else {
                            document.getElementById('optimizePrices').innerHTML = '<div style="color: #ff6b6b;">Error loading optimize prices</div>';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading optimize prices:', error);
                        document.getElementById('optimizePrices').innerHTML = '<div style="color: #ff6b6b;">Error loading optimize prices</div>';
                    });

                // Load print prices
                fetch('service_prices_api.php?action=get_prices&category=print&active_only=true')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayPrices('printPrices', data.prices);
                        } else {
                            document.getElementById('printPrices').innerHTML = '<div style="color: #ff6b6b;">Error loading print prices</div>';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading print prices:', error);
                        document.getElementById('printPrices').innerHTML = '<div style="color: #ff6b6b;">Error loading print prices</div>';
                    });
            }

            // Display prices in the UI
            function displayPrices(containerId, prices) {
                const container = document.getElementById(containerId);
                if (!prices || prices.length === 0) {
                    container.innerHTML = '<div style="color: #ccc;">No services available</div>';
                    return;
                }

                let html = '';
                prices.forEach(price => {
                    const lang = '<?= $lang ?>';
                    let itemName = price.item_name;

                    // Use localized name if available
                    if (lang === 'zh-CN' && price.item_name_zh) {
                        itemName = price.item_name_zh;
                    } else if (lang === 'en' && price.item_name_en) {
                        itemName = price.item_name_en;
                    }

                    html += `
                        <div class="price-item">
                            <div class="price-name">${itemName}</div>
                            <div>
                                <span class="price-value">$${parseFloat(price.base_price).toFixed(2)}</span>
                                <span class="price-unit">${price.unit}</span>
                            </div>
                        </div>
                    `;
                });

                container.innerHTML = html;
            }

            // Load affiliate information
            function loadAffiliateInfo() {
                console.log('Loading affiliate info...');
                
                // Create URL with relative path and timestamp to prevent caching
                const url = new URL('affiliate_api.php', window.location.href);
                url.searchParams.append('action', 'get_affiliate_info');
                url.searchParams.append('t', Date.now());
                
                console.log('Fetching from:', url.toString());
                
                console.log('Starting fetch request to:', url.toString());
                
                fetch(url, {
                    method: 'GET',
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(async response => {
                    console.log('Received response:', response.status, response.statusText);
                    
                    // First, read the response as text
                    const responseText = await response.text();
                    console.log('Raw response:', responseText);
                    
                    // Try to parse as JSON
                    try {
                        const data = responseText ? JSON.parse(responseText) : {};
                        
                        // If we got here, it's valid JSON
                        if (!response.ok) {
                            throw new Error(data.message || `HTTP error! status: ${response.status}`);
                        }
                        return data;
                    } catch (e) {
                        // If we can't parse as JSON, show the raw response
                        console.error('Failed to parse JSON:', e);
                        throw new Error(`Server returned invalid JSON: ${responseText.substring(0, 200)}`);
                    }
                })
                .then(data => {
                    console.log('Affiliate data received:', data);
                    if (data.success && data.affiliate) {
                        // Update the affiliate code display
                        document.getElementById('affiliateCode').textContent = data.affiliate.code;
                        // Update other affiliate stats
                        document.getElementById('totalReferrals').textContent = data.affiliate.total_referrals;
                        document.getElementById('totalCommissions').textContent = '$' + data.affiliate.total_commissions;
                        document.getElementById('commissionBalance').textContent = '$' + 
                            (parseFloat(data.affiliate.total_commissions) - parseFloat(data.affiliate.total_withdrawn || 0)).toFixed(2);
                        
                        // Store affiliate info for later use
                        window.affiliateInfo = data.affiliate;
                    } else {
                        const errorMsg = data && data.message ? data.message : 'No data received';
                        console.error('Failed to load affiliate info:', errorMsg);
                        document.getElementById('affiliateCode').textContent = 'Error: ' + errorMsg;
                        console.log('Full response data:', data);
                    }
                })
                .catch(error => {
                    console.error('Error in fetch operation:', error);
                    document.getElementById('affiliateCode').textContent = 'Error: ' + error.message;
                });
            }

            // Load wallet information
            function loadWalletInfo() {
                fetch(`credit_wallet.php?action=get_wallet&PHPSESSID=<?php echo session_id() ?>`, {
                    credentials: 'same-origin',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('walletBalance').textContent = '$' + parseFloat(data.balance).toFixed(2);
                        document.getElementById('totalDeposited').textContent = '$' + parseFloat(data.total_deposited || 0).toFixed(2);
                        document.getElementById('totalSpent').textContent = '$' + parseFloat(data.total_spent || 0).toFixed(2);
                        document.getElementById('frozenBalance').textContent = '$' + parseFloat(data.frozen_balance || 0).toFixed(2);
                    }
                })
                .catch(error => console.error('Error loading wallet info:', error));
            }

            // Load orders information
            function loadOrdersInfo() {
                console.log('Loading orders...');
                
                fetch(`get_user_orders.php?PHPSESSID=${SESSION_ID}`, {
                    method: 'GET',
                    credentials: 'same-origin',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    }
                })
                .then(async response => {
                    console.log('Orders response status:', response.status);
                    
                    const responseText = await response.text();
                    console.log('Orders raw response:', responseText);
                    
                    try {
                        const data = JSON.parse(responseText);
                        console.log('Orders parsed data:', data);
                        return data;
                    } catch (e) {
                        console.error('Failed to parse orders JSON:', e);
                        throw new Error('Invalid JSON response: ' + responseText.substring(0, 200));
                    }
                })
                .then(data => {
                    console.log('Orders data:', data);
                    const ordersList = document.getElementById('ordersList');
                    
                    if (data.success && data.orders && data.orders.length > 0) {
                        ordersList.innerHTML = '';
                        
                        data.orders.forEach(order => {
                            const orderDiv = document.createElement('div');
                            orderDiv.style.cssText = 'background: rgba(0,0,0,0.1); padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #00ffff;';
                            
                            const statusColor = getStatusColor(order.status);
                            
                            orderDiv.innerHTML = `
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                    <div style="font-weight: bold; color: #00ffff;">${order.order_number || 'Order #' + order.id}</div>
                                    <div style="background: ${statusColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${order.status_display || order.status}</div>
                                </div>
                                <div style="margin-bottom: 8px;">${order.order_details || 'No details available'}</div>
                                <div style="display: flex; justify-content: space-between; font-size: 14px; color: #ccc;">
                                    <div>Price: $${order.final_price || order.price || '0.00'}</div>
                                    <div>Created: ${new Date(order.created_at).toLocaleDateString()}</div>
                                </div>
                                <div style="font-size: 12px; color: #aaa; margin-top: 5px;">
                                    Service: ${order.service_type || 'General'} | Payment: ${order.payment_method || 'N/A'}
                                </div>
                            `;
                            
                            ordersList.appendChild(orderDiv);
                        });
                    } else {
                        const message = data.message || 'No orders found';
                        let debugInfo = '';
                        
                        if (data.debug) {
                            debugInfo = `<div style="font-size: 12px; color: #888; margin-top: 10px;">Debug: ${JSON.stringify(data.debug)}</div>`;
                        }
                        
                        ordersList.innerHTML = `
                            <div style="text-align: center; padding: 20px; color: #ccc;">
                                ${message}
                                ${debugInfo}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error loading orders:', error);
                    document.getElementById('ordersList').innerHTML = `
                        <div style="text-align: center; padding: 20px; color: #ff6b6b;">
                            Error loading orders: ${error.message}
                        </div>
                    `;
                });
            }
            
            // Helper function to get status color
            function getStatusColor(status) {
                const colors = {
                    'pending': '#ffa500',
                    'processing': '#2196F3',
                    'completed': '#4CAF50',
                    'cancelled': '#f44336',
                    'shipped': '#9C27B0'
                };
                return colors[status] || '#666';
            }

            // Initialize modals and other UI elements
            function initUI() {
                // Account Settings button
                document.getElementById('accountSettingsBtn').addEventListener('click', () => {
                    window.location.href = 'account_settings.php';
                });

                // Logout button
                document.getElementById('logoutBtn').addEventListener('click', () => {
                    if (confirm('Are you sure you want to log out?')) {
                        window.location.href = 'logout.php';
                    }
                });

                // Load data
                loadWalletInfo();
                loadAffiliateInfo();
                loadOrdersInfo();
                loadServicePrices();
            }

            // Initialize the page
            initUI();

            // Initialize PC Builder
            initPCBuilder();
        });

        // Order modal functions
        function openOrderModal(category) {
            // Load services for the category
            fetch(`service_prices_api.php?action=get_prices&category=${category}&active_only=true`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showOrderModal(category, data.prices);
                    } else {
                        alert('Error loading services: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading services');
                });
        }

        function showOrderModal(category, services) {
            const categoryName = category === 'optimize' ? 'Optimize Photo & Video' : 'Print Services';
            const lang = '<?= $lang ?>';

            let servicesHtml = '';
            services.forEach(service => {
                let itemName = service.item_name;
                if (lang === 'zh-CN' && service.item_name_zh) {
                    itemName = service.item_name_zh;
                } else if (lang === 'en' && service.item_name_en) {
                    itemName = service.item_name_en;
                }

                servicesHtml += `
                    <div class="service-item" data-service-id="${service.id}" data-price="${service.base_price}">
                        <div class="service-info">
                            <div class="service-name">${itemName}</div>
                            <div class="service-price">$${parseFloat(service.base_price).toFixed(2)} ${service.unit}</div>
                        </div>
                        <div class="quantity-control">
                            <button type="button" onclick="changeQuantity(${service.id}, -1)">-</button>
                            <input type="number" id="qty-${service.id}" value="0" min="0" max="100" onchange="updateQuantity(${service.id})">
                            <button type="button" onclick="changeQuantity(${service.id}, 1)">+</button>
                        </div>
                    </div>
                `;
            });

            const modalHtml = `
                <div class="order-modal-content">
                    <h2>Order ${categoryName}</h2>
                    <div class="services-list">
                        ${servicesHtml}
                    </div>
                    <div class="order-summary">
                        <div class="summary-line">
                            <span>Subtotal:</span>
                            <span id="order-subtotal">$0.00</span>
                        </div>
                        <div class="summary-line">
                            <span>Discount:</span>
                            <span id="order-discount">$0.00</span>
                        </div>
                        <div class="summary-line">
                            <span>Shipping:</span>
                            <span id="order-shipping">$0.00</span>
                        </div>
                        <div class="summary-line total">
                            <span>Total:</span>
                            <span id="order-total">$0.00</span>
                        </div>
                    </div>
                    <div class="order-options">
                        <label>
                            <input type="checkbox" id="use-credit" checked>
                            Pay with KMS Credit
                        </label>
                        <textarea id="order-notes" placeholder="Additional notes (optional)"></textarea>
                    </div>
                    <div class="order-actions">
                        <button class="btn-success" onclick="submitOrder('${category}')">Place Order</button>
                        <button class="btn-secondary" onclick="closeOrderModal()">Cancel</button>
                    </div>
                </div>
            `;

            showModal('order-modal', 'Place Order', modalHtml);
        }

        function changeQuantity(serviceId, change) {
            const input = document.getElementById(`qty-${serviceId}`);
            const currentValue = parseInt(input.value) || 0;
            const newValue = Math.max(0, Math.min(100, currentValue + change));
            input.value = newValue;
            updateQuantity(serviceId);
        }

        function updateQuantity(serviceId) {
            calculateOrderTotal();
        }

        function calculateOrderTotal() {
            const serviceItems = document.querySelectorAll('.service-item');
            let subtotal = 0;

            serviceItems.forEach(item => {
                const serviceId = item.dataset.serviceId;
                const price = parseFloat(item.dataset.price);
                const quantity = parseInt(document.getElementById(`qty-${serviceId}`).value) || 0;
                subtotal += price * quantity;
            });

            // Calculate discount (10% for orders over $50)
            const discount = subtotal > 50 ? subtotal * 0.1 : 0;

            // Calculate shipping (free for orders over $100)
            const shipping = (subtotal < 100 && subtotal > 0) ? 5.00 : 0;

            const total = subtotal - discount + shipping;

            document.getElementById('order-subtotal').textContent = '$' + subtotal.toFixed(2);
            document.getElementById('order-discount').textContent = '-$' + discount.toFixed(2);
            document.getElementById('order-shipping').textContent = '$' + shipping.toFixed(2);
            document.getElementById('order-total').textContent = '$' + total.toFixed(2);
        }

        function submitOrder(category) {
            const serviceItems = document.querySelectorAll('.service-item');
            const orderItems = [];

            serviceItems.forEach(item => {
                const serviceId = item.dataset.serviceId;
                const quantity = parseInt(document.getElementById(`qty-${serviceId}`).value) || 0;

                if (quantity > 0) {
                    orderItems.push({
                        service_id: parseInt(serviceId),
                        quantity: quantity
                    });
                }
            });

            if (orderItems.length === 0) {
                alert('Please select at least one service');
                return;
            }

            const useCredit = document.getElementById('use-credit').checked;
            const notes = document.getElementById('order-notes').value;

            const formData = new FormData();
            formData.append('action', 'create_order');
            formData.append('service_category', category);
            formData.append('order_items', JSON.stringify(orderItems));
            formData.append('use_credit', useCredit ? '1' : '0');
            formData.append('notes', notes);

            fetch('service_orders_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`Order created successfully! Order #${data.order_number}`);
                    closeOrderModal();
                    loadWalletInfo(); // Refresh wallet balance
                    loadOrdersInfo(); // Refresh orders list
                } else {
                    alert('Error creating order: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error creating order');
            });
        }

        function closeOrderModal() {
            const modal = document.getElementById('order-modal');
            if (modal) {
                modal.remove();
            }
        }

        // Modal functions - Full implementation
        function openDepositModal() {
            showModal('deposit-modal', 'Deposit Credits', `
                <div class="modal-form">
                    <div class="form-group">
                        <label>Amount (USD)</label>
                        <input type="number" id="deposit-amount" min="10" max="10000" step="0.01" placeholder="Enter amount">
                    </div>
                    <div class="form-group">
                        <label>Payment Method</label>
                        <select id="deposit-method">
                            <option value="">Select payment method...</option>
                            <option value="paypal">PayPal ($10 - $2,000)</option>
                            <option value="stripe">Credit/Debit Card ($10 - $2,000)</option>
                            <option value="bank_transfer">Bank Transfer ($50 - $5,000)</option>
                            <option value="crypto">Cryptocurrency ($25 - $10,000)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Notes (Optional)</label>
                        <textarea id="deposit-notes" placeholder="Additional notes..."></textarea>
                    </div>
                    <div id="deposit-fee-info" style="display: none; margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px;">
                        <div id="fee-breakdown"></div>
                    </div>
                </div>
            `, [
                { text: 'Cancel', class: 'secondary', onclick: 'closeModal()' },
                { text: 'Create Deposit', class: 'primary', onclick: 'submitDeposit()' }
            ]);
            
            // Add event listeners
            document.getElementById('deposit-amount').addEventListener('input', calculateDepositFees);
            document.getElementById('deposit-method').addEventListener('change', calculateDepositFees);
        }
        
        function openTransferModal() {
            showModal('transfer-modal', 'Transfer Credits', `
                <div class="modal-form">
                    <div class="form-group">
                        <label>Recipient Username</label>
                        <input type="text" id="transfer-username" placeholder="Enter username">
                    </div>
                    <div class="form-group">
                        <label>Amount (USD)</label>
                        <input type="number" id="transfer-amount" min="1" max="1000" step="0.01" placeholder="Enter amount">
                    </div>
                    <div class="form-group">
                        <label>Message (Optional)</label>
                        <textarea id="transfer-message" placeholder="Optional message..."></textarea>
                    </div>
                    <div class="transfer-limits" style="margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px; font-size: 12px;">
                        <div>• Min transfer: $1.00</div>
                        <div>• Max transfer: $1,000.00</div>
                        <div>• Daily limit: $5,000.00</div>
                    </div>
                </div>
            `, [
                { text: 'Cancel', class: 'secondary', onclick: 'closeModal()' },
                { text: 'Send Transfer', class: 'primary', onclick: 'submitTransfer()' }
            ]);
        }
        
        function openTransactionHistory() {
            showModal('history-modal', 'Transaction History', `
                <div class="history-container">
                    <div class="history-filters">
                        <select id="history-filter">
                            <option value="all">All Transactions</option>
                            <option value="deposit">Deposits</option>
                            <option value="withdraw">Withdrawals</option>
                            <option value="transfer">Transfers</option>
                            <option value="spend">Purchases</option>
                        </select>
                    </div>
                    <div id="transaction-list" style="max-height: 400px; overflow-y: auto; margin: 10px 0;">
                        <div class="loading-spinner"></div>
                    </div>
                </div>
            `, [
                { text: 'Close', class: 'secondary', onclick: 'closeModal()' }
            ]);
            
            loadTransactionHistory();
        }
        
        function openShareModal() {
            if (window.affiliateInfo && window.affiliateInfo.referral_url) {
                const url = window.affiliateInfo.referral_url;
                const code = window.affiliateInfo.code;
                
                showModal('share-modal', 'Share Referral Link', `
                    <div class="share-container">
                        <div class="form-group">
                            <label>Your Referral Code</label>
                            <div class="code-display">${code}</div>
                        </div>
                        <div class="form-group">
                            <label>Your Referral Link</label>
                            <input type="text" id="referral-link" value="${url}" readonly>
                        </div>
                        <div class="share-buttons">
                            <button onclick="copyReferralLink()" class="copy-btn">📋 Copy Link</button>
                            <button onclick="shareViaEmail()" class="email-btn">📧 Share via Email</button>
                            <button onclick="shareViaSMS()" class="sms-btn">📱 Share via SMS</button>
                        </div>
                        <div class="share-info">
                            <p>💰 Earn $50 for each successful referral!</p>
                            <p>Share this link with friends and family to start earning commissions.</p>
                        </div>
                    </div>
                `, [
                    { text: 'Close', class: 'secondary', onclick: 'closeModal()' }
                ]);
            } else {
                alert('Please wait while we load your referral information...');
            }
        }
        
        function openTransferToKMSModal() {
            // Get current commission balance
            const commissionBalance = parseFloat(document.getElementById('commissionBalance').textContent.replace('$', ''));

            if (commissionBalance <= 0) {
                alert('You have no commission balance available to transfer.');
                return;
            }

            showModal('transfer-kms-modal', 'Transfer to KMS Credit', `
                <div class="modal-form">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h3 style="color: #00ffff;">Available Commission Balance</h3>
                        <div style="font-size: 24px; color: #00ff00; font-weight: bold;">$${commissionBalance.toFixed(2)}</div>
                    </div>

                    <div class="form-group">
                        <label>Transfer Amount (USD)</label>
                        <input type="number" id="transfer-amount" min="1" max="${commissionBalance}" step="0.01"
                               placeholder="Enter amount to transfer" value="${commissionBalance}">
                        <div style="font-size: 12px; color: #ccc; margin-top: 5px;">
                            Maximum: $${commissionBalance.toFixed(2)}
                        </div>
                    </div>

                    <div style="background: rgba(0,255,255,0.1); padding: 15px; border-radius: 5px; margin: 15px 0;">
                        <h4 style="color: #00ffff; margin: 0 0 10px 0;">Transfer Details</h4>
                        <ul style="margin: 0; padding-left: 20px; color: #ccc;">
                            <li>Commission will be transferred to your KMS Credit wallet</li>
                            <li>Transfer is instant and free of charge</li>
                            <li>You can use KMS Credit for all website services</li>
                            <li>This action cannot be undone</li>
                        </ul>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="transfer-confirm" style="margin-right: 10px;">
                            I confirm that I want to transfer this commission to my KMS Credit wallet
                        </label>
                    </div>
                </div>
            `, [
                { text: 'Cancel', class: 'secondary', onclick: 'closeModal()' },
                { text: 'Transfer Now', class: 'primary', onclick: 'submitTransferToKMS()' }
            ]);
        }

        function submitTransferToKMS() {
            const amount = parseFloat(document.getElementById('transfer-amount').value);
            const confirmed = document.getElementById('transfer-confirm').checked;
            const maxAmount = parseFloat(document.getElementById('commissionBalance').textContent.replace('$', ''));

            if (!amount || amount <= 0) {
                alert('Please enter a valid transfer amount.');
                return;
            }

            if (amount > maxAmount) {
                alert('Transfer amount cannot exceed your available commission balance.');
                return;
            }

            if (!confirmed) {
                alert('Please confirm that you want to transfer this commission.');
                return;
            }

            // Submit transfer request
            fetch('affiliate_transfer.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'transfer_to_kms',
                    amount: amount
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Commission transferred successfully to your KMS Credit wallet!');
                    closeModal();
                    // Refresh wallet and affiliate info
                    loadWalletInfo();
                    loadAffiliateInfo();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while processing your transfer');
            });
        }

        function openWithdrawModal() {
            showModal('withdraw-modal', 'Withdraw Credits', `
                <div class="modal-form">
                    <div class="form-group">
                        <label>Amount (USD)</label>
                        <input type="number" id="withdraw-amount" min="25" max="10000" step="0.01" placeholder="Enter amount">
                    </div>
                    <div class="form-group">
                        <label>Withdrawal Method</label>
                        <select id="withdraw-method">
                            <option value="">Select withdrawal method...</option>
                            <option value="paypal">PayPal ($25 - $2,000)</option>
                            <option value="bank_transfer">Bank Transfer ($50 - $5,000)</option>
                            <option value="crypto">Cryptocurrency ($100 - $10,000)</option>
                            <option value="check">Paper Check ($100 - $3,000)</option>
                        </select>
                    </div>
                    <div id="withdrawal-details" style="display: none;">
                        <!-- Dynamic fields based on method -->
                    </div>
                    <div class="form-group">
                        <label>Notes (Optional)</label>
                        <textarea id="withdraw-notes" placeholder="Additional notes..."></textarea>
                    </div>
                    <div id="withdraw-fee-info" style="display: none; margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px;">
                        <div id="withdraw-fee-breakdown"></div>
                    </div>
                </div>
            `, [
                { text: 'Cancel', class: 'secondary', onclick: 'closeModal()' },
                { text: 'Request Withdrawal', class: 'primary', onclick: 'submitWithdrawal()' }
            ]);
            
            // Add event listeners
            document.getElementById('withdraw-amount').addEventListener('input', calculateWithdrawalFees);
            document.getElementById('withdraw-method').addEventListener('change', function() {
                updateWithdrawalDetails();
                calculateWithdrawalFees();
            });
        }
        
        function openAffiliateHistory() {
            showModal('affiliate-history-modal', 'Referral History', `
                <div class="history-container">
                    <div class="history-stats">
                        <div class="stat-grid">
                            <div class="stat-item">
                                <div class="stat-label">Total Referrals</div>
                                <div class="stat-value" id="total-referrals-stat">0</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Confirmed</div>
                                <div class="stat-value" id="confirmed-referrals-stat">0</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Pending</div>
                                <div class="stat-value" id="pending-referrals-stat">0</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Total Earned</div>
                                <div class="stat-value" id="total-earned-stat">$0.00</div>
                            </div>
                        </div>
                    </div>
                    <div id="referral-list" style="max-height: 400px; overflow-y: auto; margin: 10px 0;">
                        <div class="loading-spinner"></div>
                    </div>
                </div>
            `, [
                { text: 'Close', class: 'secondary', onclick: 'closeModal()' }
            ]);
            
            loadAffiliateHistory();
        }

        // PC Builder Functions
        let currentPCMode = null;
        let pcConfiguration = {};
        let selectedComponents = {};
        let prebuiltConfigs = [];
        let componentCategories = [];
        let availableComponents = {};

        function initPCBuilder() {
            loadComponentCategories();
            loadPrebuiltConfigs();
            checkAdminRestrictions();
        }

        function loadComponentCategories() {
            fetch(`pc_components_api.php?action=get_categories&PHPSESSID=${SESSION_ID}`, {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    componentCategories = data.categories;
                }
            })
            .catch(error => console.error('Error loading component categories:', error));
        }

        function loadPrebuiltConfigs() {
            fetch(`pc_components_api.php?action=get_prebuilt_configs&active_only=1&PHPSESSID=${SESSION_ID}`, {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    prebuiltConfigs = data.configs;
                }
            })
            .catch(error => console.error('Error loading prebuilt configs:', error));
        }

        function checkAdminRestrictions() {
            // This would typically check with the server for admin-set restrictions
            // For now, we'll assume no restrictions
            const restrictionInfo = document.getElementById('admin-restriction-info');
            // restrictionInfo.style.display = 'block'; // Show if there are restrictions
        }

        function selectPCMode(mode) {
            // Remove previous selection
            document.querySelectorAll('.mode-option').forEach(option => {
                option.style.border = '2px solid transparent';
            });

            // Hide all config areas
            document.querySelectorAll('.config-area').forEach(area => {
                area.style.display = 'none';
            });

            // Select current mode
            event.target.closest('.mode-option').style.border = '2px solid #00ffff';
            currentPCMode = mode;

            // Show appropriate config area
            const configArea = document.getElementById(`${mode}-mode-config`);
            if (configArea) {
                configArea.style.display = 'block';
                loadModeContent(mode);
            }

            // Show configuration summary
            document.getElementById('config-summary').style.display = 'block';
        }

        function loadModeContent(mode) {
            switch(mode) {
                case 'simple':
                    loadSimpleModeContent();
                    break;
                case 'detailed':
                    loadDetailedModeContent();
                    break;
                case 'prebuilt':
                    loadPrebuiltModeContent();
                    break;
            }
        }

        function loadSimpleModeContent() {
            const container = document.querySelector('#simple-mode-config .simple-config-grid');
            container.innerHTML = `
                <div class="config-step" style="margin-bottom: 30px;">
                    <h4 style="color: #00ffff; text-align: center; margin-bottom: 15px;">Step 1: Case Color</h4>
                    <div class="button-group" style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                        <button class="config-btn" data-step="color" data-value="white" onclick="selectSimpleOption('color', 'white', this)">
                            🤍 White
                        </button>
                        <button class="config-btn" data-step="color" data-value="black" onclick="selectSimpleOption('color', 'black', this)">
                            🖤 Black
                        </button>
                    </div>
                </div>

                <div class="config-step" style="margin-bottom: 30px; opacity: 0.5;" id="step-size">
                    <h4 style="color: #00ffff; text-align: center; margin-bottom: 15px;">Step 2: Case Size</h4>
                    <div class="button-group" style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                        <button class="config-btn" data-step="size" data-value="large" onclick="selectSimpleOption('size', 'large', this)">
                            📦 Large
                        </button>
                        <button class="config-btn" data-step="size" data-value="medium" onclick="selectSimpleOption('size', 'medium', this)">
                            📋 Medium
                        </button>
                        <button class="config-btn" data-step="size" data-value="small" onclick="selectSimpleOption('size', 'small', this)">
                            📱 Small
                        </button>
                    </div>
                </div>

                <div class="config-step" style="margin-bottom: 30px; opacity: 0.5;" id="step-use">
                    <h4 style="color: #00ffff; text-align: center; margin-bottom: 15px;">Step 3: Primary Use</h4>
                    <div class="button-group" style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                        <button class="config-btn" data-step="use" data-value="gaming" onclick="selectSimpleOption('use', 'gaming', this)">
                            🎮 Gaming
                        </button>
                        <button class="config-btn" data-step="use" data-value="video_editing" onclick="selectSimpleOption('use', 'video_editing', this)">
                            🎬 Video Editing
                        </button>
                        <button class="config-btn" data-step="use" data-value="both" onclick="selectSimpleOption('use', 'both', this)">
                            🎯 Both
                        </button>
                    </div>
                </div>

                <div class="config-step" style="margin-bottom: 30px; opacity: 0.5;" id="step-tier">
                    <h4 style="color: #00ffff; text-align: center; margin-bottom: 15px;">Step 4: Performance Tier</h4>
                    <div class="button-group" style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                        <button class="config-btn" data-step="tier" data-value="entry" onclick="selectSimpleOption('tier', 'entry', this)">
                            🥉 Entry Level
                        </button>
                        <button class="config-btn" data-step="tier" data-value="mid" onclick="selectSimpleOption('tier', 'mid', this)">
                            🥈 Mid-Range
                        </button>
                        <button class="config-btn" data-step="tier" data-value="high" onclick="selectSimpleOption('tier', 'high', this)">
                            🥇 High-End
                        </button>
                        <button class="config-btn" data-step="tier" data-value="extreme" onclick="selectSimpleOption('tier', 'extreme', this)">
                            💎 Extreme
                        </button>
                    </div>
                </div>

                <div class="config-step" style="margin-bottom: 30px; opacity: 0.5;" id="step-budget">
                    <h4 style="color: #00ffff; text-align: center; margin-bottom: 15px;">Step 5: Budget Range</h4>
                    <div class="button-group" style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                        <button class="config-btn" data-step="budget" data-value="3000" onclick="selectSimpleOption('budget', '3000', this)">
                            💰 $3,000
                        </button>
                        <button class="config-btn" data-step="budget" data-value="3500" onclick="selectSimpleOption('budget', '3500', this)">
                            💰 $3,500
                        </button>
                        <button class="config-btn" data-step="budget" data-value="4500" onclick="selectSimpleOption('budget', '4500', this)">
                            💰 $4,500
                        </button>
                        <button class="config-btn" data-step="budget" data-value="6500" onclick="selectSimpleOption('budget', '6500', this)">
                            💰 $6,500
                        </button>
                        <button class="config-btn" data-step="budget" data-value="custom" onclick="selectSimpleOption('budget', 'custom', this)">
                            ✏️ Custom
                        </button>
                    </div>
                    <div id="custom-budget-input" style="display: none; margin-top: 15px; text-align: center;">
                        <input type="number" id="custom-budget-value" placeholder="Enter custom budget"
                               style="padding: 10px; border-radius: 5px; background: #333; color: white; border: 1px solid #555; width: 200px;"
                               onchange="updateCustomBudget()">
                    </div>
                </div>
            `;

            // Add CSS for config buttons
            if (!document.getElementById('simple-mode-styles')) {
                const style = document.createElement('style');
                style.id = 'simple-mode-styles';
                style.textContent = `
                    .config-btn {
                        padding: 12px 20px;
                        border: 2px solid rgba(255,255,255,0.3);
                        border-radius: 8px;
                        background: rgba(0,0,0,0.3);
                        color: white;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
                        min-width: 120px;
                    }
                    .config-btn:hover {
                        background: rgba(0,255,255,0.2);
                        border-color: #00ffff;
                        transform: translateY(-2px);
                        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
                    }
                    .config-btn.selected {
                        background: rgba(0,255,0,0.3);
                        border-color: #00ff00;
                        color: #00ff00;
                    }
                    .config-step.disabled {
                        opacity: 0.5;
                        pointer-events: none;
                    }
                `;
                document.head.appendChild(style);
            }
        }

        // Simple Mode Configuration State
        let simpleConfig = {
            color: null,
            size: null,
            use: null,
            tier: null,
            budget: null
        };

        function selectSimpleOption(step, value, button) {
            // Update configuration
            simpleConfig[step] = value;

            // Update button states for this step
            const stepButtons = document.querySelectorAll(`[data-step="${step}"]`);
            stepButtons.forEach(btn => btn.classList.remove('selected'));
            button.classList.add('selected');

            // Handle custom budget input
            if (step === 'budget') {
                const customInput = document.getElementById('custom-budget-input');
                if (value === 'custom') {
                    customInput.style.display = 'block';
                } else {
                    customInput.style.display = 'none';
                }
            }

            // Enable next step
            enableNextStep(step);

            // Update configuration
            updateSimpleConfig();
        }

        function enableNextStep(currentStep) {
            const stepOrder = ['color', 'size', 'use', 'tier', 'budget'];
            const currentIndex = stepOrder.indexOf(currentStep);

            if (currentIndex < stepOrder.length - 1) {
                const nextStep = stepOrder[currentIndex + 1];
                const nextStepElement = document.getElementById(`step-${nextStep}`);
                if (nextStepElement) {
                    nextStepElement.style.opacity = '1';
                    nextStepElement.classList.remove('disabled');
                }
            }
        }

        function updateCustomBudget() {
            const customValue = document.getElementById('custom-budget-value').value;
            if (customValue) {
                simpleConfig.budget = customValue;
                updateSimpleConfig();
            }
        }

        function updateSimpleConfig() {
            pcConfiguration = {
                mode: 'simple',
                case_color: simpleConfig.color,
                case_size: simpleConfig.size,
                primary_use: simpleConfig.use,
                tier: simpleConfig.tier,
                budget_range: simpleConfig.budget
            };

            updateConfigurationSummary();
        }

        function loadDetailedModeContent() {
            const container = document.querySelector('#detailed-mode-config .detailed-config-container');
            container.innerHTML = `
                <div class="detailed-config-sections">
                    <div class="component-category" style="margin-bottom: 30px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 15px;">🖥️ CPU (Processor)</h4>
                        <div class="component-options" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                            <div class="component-card" onclick="selectComponent('cpu', 'intel-i5-13600k')" style="padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; border: 2px solid transparent; cursor: pointer;">
                                <h5 style="color: #00ffff; margin: 0 0 5px 0;">Intel i5-13600K</h5>
                                <p style="font-size: 12px; color: #ccc; margin: 0;">14 cores, 20 threads, 3.5GHz base</p>
                                <div style="color: #00ff00; font-weight: bold; margin-top: 10px;">$329</div>
                            </div>
                            <div class="component-card" onclick="selectComponent('cpu', 'intel-i7-13700k')" style="padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; border: 2px solid transparent; cursor: pointer;">
                                <h5 style="color: #00ffff; margin: 0 0 5px 0;">Intel i7-13700K</h5>
                                <p style="font-size: 12px; color: #ccc; margin: 0;">16 cores, 24 threads, 3.4GHz base</p>
                                <div style="color: #00ff00; font-weight: bold; margin-top: 10px;">$419</div>
                            </div>
                            <div class="component-card" onclick="selectComponent('cpu', 'amd-ryzen-7-7700x')" style="padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; border: 2px solid transparent; cursor: pointer;">
                                <h5 style="color: #00ffff; margin: 0 0 5px 0;">AMD Ryzen 7 7700X</h5>
                                <p style="font-size: 12px; color: #ccc; margin: 0;">8 cores, 16 threads, 4.5GHz base</p>
                                <div style="color: #00ff00; font-weight: bold; margin-top: 10px;">$399</div>
                            </div>
                        </div>
                    </div>

                    <div class="component-category" style="margin-bottom: 30px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 15px;">🎮 GPU (Graphics Card)</h4>
                        <div class="component-options" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                            <div class="component-card" onclick="selectComponent('gpu', 'rtx-4060')" style="padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; border: 2px solid transparent; cursor: pointer;">
                                <h5 style="color: #00ffff; margin: 0 0 5px 0;">RTX 4060</h5>
                                <p style="font-size: 12px; color: #ccc; margin: 0;">8GB GDDR6, 1080p Gaming</p>
                                <div style="color: #00ff00; font-weight: bold; margin-top: 10px;">$299</div>
                            </div>
                            <div class="component-card" onclick="selectComponent('gpu', 'rtx-4070')" style="padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; border: 2px solid transparent; cursor: pointer;">
                                <h5 style="color: #00ffff; margin: 0 0 5px 0;">RTX 4070</h5>
                                <p style="font-size: 12px; color: #ccc; margin: 0;">12GB GDDR6X, 1440p Gaming</p>
                                <div style="color: #00ff00; font-weight: bold; margin-top: 10px;">$599</div>
                            </div>
                            <div class="component-card" onclick="selectComponent('gpu', 'rtx-4080')" style="padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; border: 2px solid transparent; cursor: pointer;">
                                <h5 style="color: #00ffff; margin: 0 0 5px 0;">RTX 4080</h5>
                                <p style="font-size: 12px; color: #ccc; margin: 0;">16GB GDDR6X, 4K Gaming</p>
                                <div style="color: #00ff00; font-weight: bold; margin-top: 10px;">$1199</div>
                            </div>
                        </div>
                    </div>

                    <div class="component-category" style="margin-bottom: 30px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 15px;">💾 RAM (Memory)</h4>
                        <div class="component-options" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div class="component-card" onclick="selectComponent('ram', '16gb-ddr4')" style="padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; border: 2px solid transparent; cursor: pointer;">
                                <h5 style="color: #00ffff; margin: 0 0 5px 0;">16GB DDR4-3200</h5>
                                <p style="font-size: 12px; color: #ccc; margin: 0;">2x8GB Kit</p>
                                <div style="color: #00ff00; font-weight: bold; margin-top: 10px;">$89</div>
                            </div>
                            <div class="component-card" onclick="selectComponent('ram', '32gb-ddr4')" style="padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; border: 2px solid transparent; cursor: pointer;">
                                <h5 style="color: #00ffff; margin: 0 0 5px 0;">32GB DDR4-3200</h5>
                                <p style="font-size: 12px; color: #ccc; margin: 0;">2x16GB Kit</p>
                                <div style="color: #00ff00; font-weight: bold; margin-top: 10px;">$179</div>
                            </div>
                            <div class="component-card" onclick="selectComponent('ram', '32gb-ddr5')" style="padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; border: 2px solid transparent; cursor: pointer;">
                                <h5 style="color: #00ffff; margin: 0 0 5px 0;">32GB DDR5-5600</h5>
                                <p style="font-size: 12px; color: #ccc; margin: 0;">2x16GB Kit</p>
                                <div style="color: #00ff00; font-weight: bold; margin-top: 10px;">$299</div>
                            </div>
                        </div>
                    </div>

                    <div class="component-category" style="margin-bottom: 30px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 15px;">💽 Storage</h4>
                        <div class="component-options" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div class="component-card" onclick="selectComponent('storage', '1tb-nvme')" style="padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; border: 2px solid transparent; cursor: pointer;">
                                <h5 style="color: #00ffff; margin: 0 0 5px 0;">1TB NVMe SSD</h5>
                                <p style="font-size: 12px; color: #ccc; margin: 0;">PCIe 4.0, 7000MB/s</p>
                                <div style="color: #00ff00; font-weight: bold; margin-top: 10px;">$129</div>
                            </div>
                            <div class="component-card" onclick="selectComponent('storage', '2tb-nvme')" style="padding: 15px; background: rgba(0,0,0,0.3); border-radius: 8px; border: 2px solid transparent; cursor: pointer;">
                                <h5 style="color: #00ffff; margin: 0 0 5px 0;">2TB NVMe SSD</h5>
                                <p style="font-size: 12px; color: #ccc; margin: 0;">PCIe 4.0, 7000MB/s</p>
                                <div style="color: #00ff00; font-weight: bold; margin-top: 10px;">$249</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add component selection styles
            if (!document.getElementById('detailed-mode-styles')) {
                const style = document.createElement('style');
                style.id = 'detailed-mode-styles';
                style.textContent = `
                    .component-card {
                        transition: all 0.3s ease;
                    }
                    .component-card:hover {
                        border-color: #4ecdc4 !important;
                        background: rgba(78, 205, 196, 0.1) !important;
                        transform: translateY(-2px);
                        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
                    }
                    .component-card.selected {
                        border-color: #00ff00 !important;
                        background: rgba(0, 255, 0, 0.1) !important;
                    }
                `;
                document.head.appendChild(style);
            }
        }

        function loadPrebuiltModeContent() {
            const container = document.querySelector('#prebuilt-mode-config .prebuilt-configs-grid');

            // Sample pre-built configurations
            const sampleConfigs = [
                {
                    id: 1,
                    name: "Gaming Starter",
                    description: "Perfect for 1080p gaming",
                    specs: {
                        "CPU": "Intel i5-13400F",
                        "GPU": "RTX 4060",
                        "RAM": "16GB DDR4",
                        "Storage": "1TB NVMe SSD",
                        "Case": "Mid Tower RGB"
                    },
                    price: 1299,
                    originalPrice: 1499,
                    discount: 13
                },
                {
                    id: 2,
                    name: "Performance Pro",
                    description: "High-end gaming & content creation",
                    specs: {
                        "CPU": "Intel i7-13700K",
                        "GPU": "RTX 4070 Ti",
                        "RAM": "32GB DDR5",
                        "Storage": "2TB NVMe SSD",
                        "Case": "Full Tower RGB"
                    },
                    price: 2299,
                    originalPrice: 2599,
                    discount: 12
                },
                {
                    id: 3,
                    name: "Ultimate Beast",
                    description: "No compromises 4K gaming",
                    specs: {
                        "CPU": "Intel i9-13900K",
                        "GPU": "RTX 4080",
                        "RAM": "32GB DDR5",
                        "Storage": "2TB NVMe SSD + 2TB HDD",
                        "Case": "Premium RGB Tower"
                    },
                    price: 3499,
                    originalPrice: 3899,
                    discount: 10
                },
                {
                    id: 4,
                    name: "Content Creator",
                    description: "Optimized for video editing",
                    specs: {
                        "CPU": "AMD Ryzen 9 7900X",
                        "GPU": "RTX 4070",
                        "RAM": "64GB DDR5",
                        "Storage": "4TB NVMe SSD",
                        "Case": "Workstation Tower"
                    },
                    price: 2899,
                    originalPrice: 3199,
                    discount: 9
                }
            ];

            container.innerHTML = sampleConfigs.map(config => `
                <div class="prebuilt-config-card" style="padding: 20px; background: rgba(0,0,0,0.3); border-radius: 10px; border: 2px solid transparent; cursor: pointer; transition: all 0.3s ease;" onclick="selectPrebuiltConfig(${config.id}, '${config.name}', ${config.price})">
                    <h4 style="color: #ffd700; margin-bottom: 10px; text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">${config.name}</h4>
                    <p style="color: #ccc; font-size: 14px; margin-bottom: 15px;">${config.description}</p>

                    <div class="config-specs" style="margin-bottom: 15px;">
                        ${Object.entries(config.specs).map(([key, value]) =>
                            `<div style="margin: 3px 0; font-size: 13px; display: flex; justify-content: space-between;">
                                <span style="color: #00ffff;">${key}:</span>
                                <span style="color: #fff;">${value}</span>
                            </div>`
                        ).join('')}
                    </div>

                    <div class="config-price" style="text-align: center; border-top: 1px solid rgba(255,255,255,0.2); padding-top: 15px;">
                        ${config.discount > 0 ?
                            `<div style="color: #ff6b6b; text-decoration: line-through; font-size: 16px;">$${config.originalPrice.toLocaleString()}</div>
                             <div style="color: #00ff00; font-size: 24px; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">$${config.price.toLocaleString()}</div>
                             <div style="color: #ffd700; font-size: 12px; background: rgba(255,215,0,0.2); padding: 2px 8px; border-radius: 10px; display: inline-block; margin-top: 5px;">${config.discount}% OFF</div>` :
                            `<div style="color: #00ff00; font-size: 24px; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">$${config.price.toLocaleString()}</div>`
                        }
                    </div>
                </div>
            `).join('');

            // Add hover effects
            if (!document.getElementById('prebuilt-styles')) {
                const style = document.createElement('style');
                style.id = 'prebuilt-styles';
                style.textContent = `
                    .prebuilt-config-card:hover {
                        border-color: #ffd700 !important;
                        background: rgba(255, 215, 0, 0.1) !important;
                        transform: translateY(-3px);
                        box-shadow: 0 6px 12px rgba(0,0,0,0.4);
                    }
                    .prebuilt-config-card.selected {
                        border-color: #00ff00 !important;
                        background: rgba(0, 255, 0, 0.1) !important;
                    }
                `;
                document.head.appendChild(style);
            }
        }

        // Detailed Mode Configuration State
        let detailedConfig = {
            cpu: null,
            gpu: null,
            ram: null,
            storage: null
        };

        function selectComponent(category, componentId) {
            // Update configuration
            detailedConfig[category] = componentId;

            // Update visual selection
            const categoryCards = document.querySelectorAll(`[onclick*="selectComponent('${category}',"]`);
            categoryCards.forEach(card => card.classList.remove('selected'));
            event.target.closest('.component-card').classList.add('selected');

            // Update configuration
            updateDetailedConfig();
        }

        function updateDetailedConfig() {
            // Component prices (sample data)
            const componentPrices = {
                'intel-i5-13600k': 329,
                'intel-i7-13700k': 419,
                'amd-ryzen-7-7700x': 399,
                'rtx-4060': 299,
                'rtx-4070': 599,
                'rtx-4080': 1199,
                '16gb-ddr4': 89,
                '32gb-ddr4': 179,
                '32gb-ddr5': 299,
                '1tb-nvme': 129,
                '2tb-nvme': 249
            };

            let totalPrice = 0;
            Object.values(detailedConfig).forEach(component => {
                if (component && componentPrices[component]) {
                    totalPrice += componentPrices[component];
                }
            });

            pcConfiguration = {
                mode: 'detailed',
                components: detailedConfig,
                estimated_price: totalPrice
            };

            updateConfigurationSummary();
        }

        function selectPrebuiltConfig(configId, configName, price) {
            // Remove previous selection
            document.querySelectorAll('.prebuilt-config-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Select current config
            event.target.closest('.prebuilt-config-card').classList.add('selected');

            pcConfiguration = {
                mode: 'prebuilt',
                config_id: configId,
                config_name: configName,
                price: price
            };

            updateConfigurationSummary();
        }

        function updateConfigurationSummary() {
            const summaryContent = document.getElementById('summary-content');
            const estimatedTotal = document.getElementById('estimated-total');

            let summaryHtml = '';
            let totalPrice = 0;

            if (pcConfiguration.mode === 'simple') {
                summaryHtml = `
                    <div class="summary-item" style="margin: 5px 0;"><strong>Mode:</strong> Simple Configuration</div>
                    <div class="summary-item" style="margin: 5px 0;"><strong>Case Color:</strong> ${pcConfiguration.case_color || 'Not selected'}</div>
                    <div class="summary-item" style="margin: 5px 0;"><strong>Case Size:</strong> ${pcConfiguration.case_size || 'Not selected'}</div>
                    <div class="summary-item" style="margin: 5px 0;"><strong>Primary Use:</strong> ${pcConfiguration.primary_use || 'Not selected'}</div>
                    <div class="summary-item" style="margin: 5px 0;"><strong>Performance Tier:</strong> ${pcConfiguration.tier || 'Not selected'}</div>
                    <div class="summary-item" style="margin: 5px 0;"><strong>Budget Range:</strong> ${pcConfiguration.budget_range ? (pcConfiguration.budget_range === 'custom' ? 'Custom Budget' : '$' + pcConfiguration.budget_range) : 'Not selected'}</div>
                `;
                // Estimate price based on budget or tier
                if (pcConfiguration.budget_range && pcConfiguration.budget_range !== 'custom') {
                    totalPrice = parseInt(pcConfiguration.budget_range) || 0;
                } else {
                    const tierPrices = { entry: 1400, mid: 1800, high: 2900, extreme: 4400 };
                    totalPrice = tierPrices[pcConfiguration.tier] || 0;
                }
            } else if (pcConfiguration.mode === 'detailed') {
                summaryHtml = `
                    <div class="summary-item" style="margin: 5px 0;"><strong>Mode:</strong> Detailed Configuration</div>
                `;
                const componentNames = {
                    'intel-i5-13600k': 'Intel i5-13600K',
                    'intel-i7-13700k': 'Intel i7-13700K',
                    'amd-ryzen-7-7700x': 'AMD Ryzen 7 7700X',
                    'rtx-4060': 'RTX 4060',
                    'rtx-4070': 'RTX 4070',
                    'rtx-4080': 'RTX 4080',
                    '16gb-ddr4': '16GB DDR4-3200',
                    '32gb-ddr4': '32GB DDR4-3200',
                    '32gb-ddr5': '32GB DDR5-5600',
                    '1tb-nvme': '1TB NVMe SSD',
                    '2tb-nvme': '2TB NVMe SSD'
                };

                Object.entries(pcConfiguration.components).forEach(([category, component]) => {
                    const displayName = componentNames[component] || component || 'Not selected';
                    summaryHtml += `<div class="summary-item" style="margin: 5px 0;"><strong>${category.toUpperCase()}:</strong> ${displayName}</div>`;
                });

                totalPrice = pcConfiguration.estimated_price || 0;
            } else if (pcConfiguration.mode === 'prebuilt') {
                summaryHtml = `
                    <div class="summary-item" style="margin: 5px 0;"><strong>Mode:</strong> Pre-built Configuration</div>
                    <div class="summary-item" style="margin: 5px 0;"><strong>Selected Config:</strong> ${pcConfiguration.config_name}</div>
                `;
                totalPrice = parseFloat(pcConfiguration.price) || 0;
            }

            summaryContent.innerHTML = summaryHtml;
            estimatedTotal.textContent = `$${totalPrice.toFixed(2)}`;
        }

        function requestPCQuote() {
            if (!pcConfiguration.mode) {
                alert('Please select a configuration mode first.');
                return;
            }

            // Validate configuration based on mode
            if (pcConfiguration.mode === 'simple') {
                if (!pcConfiguration.primary_use || !pcConfiguration.tier) {
                    alert('Please complete all required fields in simple mode.');
                    return;
                }
            } else if (pcConfiguration.mode === 'prebuilt') {
                if (!pcConfiguration.prebuilt_config_id) {
                    alert('Please select a pre-built configuration.');
                    return;
                }
            }

            // Create PC order
            const formData = new FormData();
            formData.append('action', 'create_pc_order');
            formData.append('order_type', pcConfiguration.mode);
            formData.append('configuration', JSON.stringify(pcConfiguration));
            if (pcConfiguration.prebuilt_config_id) {
                formData.append('prebuilt_config_id', pcConfiguration.prebuilt_config_id);
            }
            formData.append('notes', 'PC Builder configuration request');

            fetch(`pc_components_api.php?PHPSESSID=${SESSION_ID}`, {
                method: 'POST',
                credentials: 'same-origin',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('PC quote request submitted successfully! Order number: ' + data.order_number);
                    // Reset configuration
                    pcConfiguration = {};
                    document.getElementById('config-summary').style.display = 'none';
                    document.querySelectorAll('.mode-option').forEach(option => {
                        option.style.border = '2px solid transparent';
                    });
                    document.querySelectorAll('.config-area').forEach(area => {
                        area.style.display = 'none';
                    });
                    currentPCMode = null;

                    // Reload orders
                    loadOrdersInfo();
                } else {
                    alert('Failed to submit PC quote request: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error submitting PC quote:', error);
                alert('Error submitting PC quote request.');
            });
        }

        // Modal utility functions
        function showModal(id, title, content, buttons = []) {
            // Remove existing modal if any
            const existingModal = document.getElementById('custom-modal');
            if (existingModal) {
                existingModal.remove();
            }
            
            const modal = document.createElement('div');
            modal.id = 'custom-modal';
            modal.className = 'custom-modal';
            modal.style.display = 'block';
            
            const buttonsHtml = buttons.map(btn =>
                `<button class="custom-modal-btn ${btn.class}" onclick="${btn.onclick}">${btn.text}</button>`
            ).join('');
            
            modal.innerHTML = `
                <div class="custom-modal-content">
                    <div class="custom-modal-header">
                        <h3>${title}</h3>
                        <span class="custom-modal-close" onclick="closeModal()">&times;</span>
                    </div>
                    <div class="custom-modal-body">
                        ${content}
                    </div>
                    <div class="custom-modal-footer">
                        ${buttonsHtml}
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Close modal when clicking outside
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });
        }
        
        function closeModal() {
            const modal = document.getElementById('custom-modal');
            if (modal) {
                modal.remove();
            }
        }
        
        // Deposit functions
        function calculateDepositFees() {
            const amount = parseFloat(document.getElementById('deposit-amount').value) || 0;
            const method = document.getElementById('deposit-method').value;
            
            if (amount <= 0 || !method) {
                document.getElementById('deposit-fee-info').style.display = 'none';
                return;
            }
            
            const feeRates = {
                'paypal': { percentage: 2.9, fixed: 0.30 },
                'stripe': { percentage: 2.9, fixed: 0.30 },
                'bank_transfer': { percentage: 0.0, fixed: 0.0 },
                'crypto': { percentage: 1.0, fixed: 0.0 }
            };
            
            const rates = feeRates[method] || { percentage: 2.9, fixed: 0.30 };
            const processingFee = (amount * rates.percentage / 100) + rates.fixed;
            const netAmount = amount - processingFee;
            
            document.getElementById('deposit-fee-info').style.display = 'block';
            document.getElementById('fee-breakdown').innerHTML = `
                <div>Amount: $${amount.toFixed(2)}</div>
                <div>Processing Fee: $${processingFee.toFixed(2)}</div>
                <div><strong>Net Amount: $${netAmount.toFixed(2)}</strong></div>
            `;
        }
        
        function submitDeposit() {
            const amount = parseFloat(document.getElementById('deposit-amount').value);
            const method = document.getElementById('deposit-method').value;
            const notes = document.getElementById('deposit-notes').value;
            
            if (!amount || amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }
            
            if (!method) {
                alert('Please select a payment method');
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'create_deposit');
            formData.append('amount', amount);
            formData.append('payment_method', method);
            formData.append('notes', notes);
            
            fetch('deposit_api.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Deposit request created successfully! Deposit ID: ' + data.deposit_id);
                    closeModal();
                    loadWalletInfo(); // Refresh wallet info
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while processing your deposit');
            });
        }
        
        // Transfer functions
        function submitTransfer() {
            const username = document.getElementById('transfer-username').value.trim();
            const amount = parseFloat(document.getElementById('transfer-amount').value);
            const message = document.getElementById('transfer-message').value;
            
            if (!username) {
                alert('Please enter a recipient username');
                return;
            }
            
            if (!amount || amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }
            
            if (!confirm(`Transfer $${amount.toFixed(2)} to ${username}?`)) {
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'transfer');
            formData.append('to_username', username);
            formData.append('amount', amount);
            formData.append('message', message);
            
            fetch('credit_wallet.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Transfer completed successfully!');
                    closeModal();
                    loadWalletInfo(); // Refresh wallet info
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while processing your transfer');
            });
        }
        
        // Transaction history functions
        function loadTransactionHistory() {
            fetch(`credit_wallet.php?action=get_transactions&PHPSESSID=${SESSION_ID}`, {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                const listContainer = document.getElementById('transaction-list');
                
                if (data.success && data.transactions && data.transactions.length > 0) {
                    listContainer.innerHTML = '';
                    
                    data.transactions.forEach(transaction => {
                        const transactionDiv = document.createElement('div');
                        transactionDiv.className = 'transaction-item';
                        transactionDiv.style.cssText = 'padding: 10px; margin: 5px 0; background: rgba(255,255,255,0.1); border-radius: 5px; border-left: 4px solid #00ffff;';
                        
                        const typeColor = getTransactionTypeColor(transaction.type);
                        
                        transactionDiv.innerHTML = `
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong>${transaction.type_display}</strong>
                                    <div style="font-size: 12px; color: #ccc;">${transaction.description}</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: ${typeColor}; font-weight: bold;">${transaction.amount_display}</div>
                                    <div style="font-size: 12px; color: #ccc;">${transaction.created_at}</div>
                                </div>
                            </div>
                        `;
                        
                        listContainer.appendChild(transactionDiv);
                    });
                } else {
                    listContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #ccc;">No transactions found</div>';
                }
            })
            .catch(error => {
                console.error('Error loading transaction history:', error);
                document.getElementById('transaction-list').innerHTML = '<div style="text-align: center; padding: 20px; color: #ff6b6b;">Error loading transactions</div>';
            });
        }
        
        function getTransactionTypeColor(type) {
            const colors = {
                'deposit': '#4CAF50',
                'refund': '#4CAF50',
                'transfer_in': '#4CAF50',
                'withdraw': '#f44336',
                'spend': '#f44336',
                'transfer_out': '#f44336',
                'admin_adjust': '#ff9800'
            };
            return colors[type] || '#666';
        }
        
        // Withdrawal functions
        function updateWithdrawalDetails() {
            const method = document.getElementById('withdraw-method').value;
            const detailsContainer = document.getElementById('withdrawal-details');
            
            if (!method) {
                detailsContainer.style.display = 'none';
                return;
            }
            
            detailsContainer.style.display = 'block';
            
            let fieldsHtml = '';
            
            switch (method) {
                case 'paypal':
                    fieldsHtml = `
                        <div class="form-group">
                            <label>PayPal Email</label>
                            <input type="email" id="paypal-email" placeholder="<EMAIL>" required>
                        </div>
                    `;
                    break;
                case 'bank_transfer':
                    fieldsHtml = `
                        <div class="form-group">
                            <label>Account Holder Name</label>
                            <input type="text" id="account-holder" placeholder="Full name" required>
                        </div>
                        <div class="form-group">
                            <label>Bank Name</label>
                            <input type="text" id="bank-name" placeholder="Bank name" required>
                        </div>
                        <div class="form-group">
                            <label>Account Number</label>
                            <input type="text" id="account-number" placeholder="Account number" required>
                        </div>
                        <div class="form-group">
                            <label>Routing Number</label>
                            <input type="text" id="routing-number" placeholder="Routing number" required>
                        </div>
                    `;
                    break;
                case 'crypto':
                    fieldsHtml = `
                        <div class="form-group">
                            <label>Cryptocurrency</label>
                            <select id="crypto-type" required>
                                <option value="">Select cryptocurrency</option>
                                <option value="bitcoin">Bitcoin</option>
                                <option value="ethereum">Ethereum</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Wallet Address</label>
                            <input type="text" id="wallet-address" placeholder="Your wallet address" required>
                        </div>
                    `;
                    break;
                case 'check':
                    fieldsHtml = `
                        <div class="form-group">
                            <label>Full Name</label>
                            <input type="text" id="check-name" placeholder="Full name for check" required>
                        </div>
                        <div class="form-group">
                            <label>Address</label>
                            <input type="text" id="check-address" placeholder="Street address" required>
                        </div>
                        <div class="form-group">
                            <label>City</label>
                            <input type="text" id="check-city" placeholder="City" required>
                        </div>
                        <div class="form-group">
                            <label>State</label>
                            <input type="text" id="check-state" placeholder="State" required>
                        </div>
                        <div class="form-group">
                            <label>ZIP Code</label>
                            <input type="text" id="check-zip" placeholder="ZIP code" required>
                        </div>
                    `;
                    break;
            }
            
            detailsContainer.innerHTML = fieldsHtml;
        }
        
        function calculateWithdrawalFees() {
            const amount = parseFloat(document.getElementById('withdraw-amount').value) || 0;
            const method = document.getElementById('withdraw-method').value;
            
            if (amount <= 0 || !method) {
                document.getElementById('withdraw-fee-info').style.display = 'none';
                return;
            }
            
            const feeRates = {
                'paypal': { percentage: 1.0, fixed: 0.0 },
                'bank_transfer': { percentage: 0.0, fixed: 2.50 },
                'crypto': { percentage: 2.0, fixed: 0.0 },
                'check': { percentage: 0.0, fixed: 5.00 }
            };
            
            const rates = feeRates[method] || { percentage: 1.0, fixed: 0.0 };
            const processingFee = (amount * rates.percentage / 100) + rates.fixed;
            const netAmount = amount - processingFee;
            
            document.getElementById('withdraw-fee-info').style.display = 'block';
            document.getElementById('withdraw-fee-breakdown').innerHTML = `
                <div>Withdrawal Amount: $${amount.toFixed(2)}</div>
                <div>Processing Fee: $${processingFee.toFixed(2)}</div>
                <div><strong>You will receive: $${netAmount.toFixed(2)}</strong></div>
            `;
        }
        
        function submitWithdrawal() {
            const amount = parseFloat(document.getElementById('withdraw-amount').value);
            const method = document.getElementById('withdraw-method').value;
            const notes = document.getElementById('withdraw-notes').value;
            
            if (!amount || amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }
            
            if (!method) {
                alert('Please select a withdrawal method');
                return;
            }
            
            // Collect method-specific details
            const details = {};
            
            switch (method) {
                case 'paypal':
                    details.paypal_email = document.getElementById('paypal-email').value;
                    break;
                case 'bank_transfer':
                    details.account_holder_name = document.getElementById('account-holder').value;
                    details.bank_name = document.getElementById('bank-name').value;
                    details.account_number = document.getElementById('account-number').value;
                    details.routing_number = document.getElementById('routing-number').value;
                    break;
                case 'crypto':
                    details.crypto_type = document.getElementById('crypto-type').value;
                    details.wallet_address = document.getElementById('wallet-address').value;
                    break;
                case 'check':
                    details.full_name = document.getElementById('check-name').value;
                    details.address = document.getElementById('check-address').value;
                    details.city = document.getElementById('check-city').value;
                    details.state = document.getElementById('check-state').value;
                    details.zip_code = document.getElementById('check-zip').value;
                    break;
            }
            
            if (!confirm(`Request withdrawal of $${amount.toFixed(2)} via ${method}?`)) {
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'create_withdrawal');
            formData.append('amount', amount);
            formData.append('withdrawal_method', method);
            formData.append('withdrawal_details', JSON.stringify(details));
            formData.append('notes', notes);
            
            fetch('withdraw_api.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Withdrawal request submitted successfully! Request ID: ' + data.withdrawal_id);
                    closeModal();
                    loadWalletInfo(); // Refresh wallet info
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while processing your withdrawal');
            });
        }
        
        // Share functions
        function copyReferralLink() {
            const linkInput = document.getElementById('referral-link');
            linkInput.select();
            document.execCommand('copy');
            alert('Referral link copied to clipboard!');
        }
        
        function shareViaEmail() {
            const link = document.getElementById('referral-link').value;
            const subject = 'Join KMS and get started with amazing services!';
            const body = `Hi! I'd like to invite you to join KMS. Use my referral link to get started: ${link}`;
            window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
        }
        
        function shareViaSMS() {
            const link = document.getElementById('referral-link').value;
            const message = `Check out KMS! Use my referral link: ${link}`;
            window.open(`sms:?body=${encodeURIComponent(message)}`);
        }
        
        // Affiliate history functions
        function loadAffiliateHistory() {
            fetch(`affiliate_api.php?action=get_referral_history&PHPSESSID=${SESSION_ID}`, {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                const listContainer = document.getElementById('referral-list');
                
                if (data.success && data.referrals && data.referrals.length > 0) {
                    listContainer.innerHTML = '';
                    
                    // Update stats
                    const stats = calculateReferralStats(data.referrals);
                    document.getElementById('total-referrals-stat').textContent = stats.total;
                    document.getElementById('confirmed-referrals-stat').textContent = stats.confirmed;
                    document.getElementById('pending-referrals-stat').textContent = stats.pending;
                    document.getElementById('total-earned-stat').textContent = '$' + stats.totalEarned.toFixed(2);
                    
                    data.referrals.forEach(referral => {
                        const referralDiv = document.createElement('div');
                        referralDiv.className = 'referral-item';
                        referralDiv.style.cssText = 'padding: 10px; margin: 5px 0; background: rgba(255,255,255,0.1); border-radius: 5px; border-left: 4px solid #00ffff;';
                        
                        const statusColor = referral.status === 'confirmed' ? '#4CAF50' : '#ff9800';
                        const commissionStatus = referral.commission_status === 'paid' ? 'Paid' : 'Pending';
                        
                        referralDiv.innerHTML = `
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong>${referral.referred_username}</strong>
                                    <div style="font-size: 12px; color: #ccc;">${referral.referred_email}</div>
                                    <div style="font-size: 12px; color: ${statusColor};">Status: ${referral.status}</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #4CAF50; font-weight: bold;">$${referral.commission_amount}</div>
                                    <div style="font-size: 12px; color: #ccc;">${commissionStatus}</div>
                                    <div style="font-size: 12px; color: #ccc;">${referral.created_at}</div>
                                </div>
                            </div>
                        `;
                        
                        listContainer.appendChild(referralDiv);
                    });
                } else {
                    listContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #ccc;">No referrals found</div>';
                }
            })
            .catch(error => {
                console.error('Error loading affiliate history:', error);
                document.getElementById('referral-list').innerHTML = '<div style="text-align: center; padding: 20px; color: #ff6b6b;">Error loading referral history</div>';
            });
        }
        
        function calculateReferralStats(referrals) {
            const stats = {
                total: referrals.length,
                confirmed: 0,
                pending: 0,
                totalEarned: 0
            };
            
            referrals.forEach(referral => {
                if (referral.status === 'confirmed') {
                    stats.confirmed++;
                } else {
                    stats.pending++;
                }
                
                if (referral.commission_status === 'paid') {
                    stats.totalEarned += parseFloat(referral.commission_amount || 0);
                }
            });
            
            return stats;
        }

        // Heartbeat function to keep user online
        function sendHeartbeat() {
            fetch('heartbeat.php', {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status !== 'success') {
                    console.warn('Heartbeat failed:', data.message);
                }
            })
            .catch(error => {
                console.error('Heartbeat error:', error);
            });
        }

        // Send heartbeat every 2 minutes to keep user online
        setInterval(sendHeartbeat, 2 * 60 * 1000); // 2 minutes

        // Send initial heartbeat
        sendHeartbeat();
    </script>
    
    <style>
        .modal-form .form-group {
            margin-bottom: 15px;
        }
        
        .modal-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #fff;
        }
        
        .modal-form input, .modal-form select, .modal-form textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            background: rgba(255,255,255,0.9);
            color: #333;
        }
        
        .modal-form textarea {
            height: 80px;
            resize: vertical;
        }
        
        .share-container .code-display {
            background: rgba(0,0,0,0.2);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            color: #00ff00;
            margin: 10px 0;
        }
        
        .share-buttons {
            display: flex;
            gap: 10px;
            margin: 15px 0;
        }
        
        .share-buttons button {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .copy-btn { background: #4CAF50; color: white; }
        .email-btn { background: #2196F3; color: white; }
        .sms-btn { background: #ff9800; color: white; }
        
        .share-info {
            background: rgba(0,0,0,0.2);
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .share-info p {
            margin: 5px 0;
            color: #fff;
        }
        
        .history-container {
            max-width: 100%;
        }
        
        .history-filters {
            margin-bottom: 15px;
        }
        
        .history-filters select {
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #ddd;
            background: rgba(255,255,255,0.9);
        }
        
        .history-stats {
            margin-bottom: 15px;
        }
        
        .stat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
        }
        
        .stat-grid .stat-item {
            background: rgba(0,0,0,0.2);
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .stat-grid .stat-label {
            font-size: 12px;
            color: #ccc;
            margin-bottom: 5px;
        }
        
        .stat-grid .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #00ffff;
        }
        
        /* Custom Modal Styles */
        .custom-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
        }
        
        .custom-modal-content {
            background-color: #2b9869;
            margin: 5% auto;
            padding: 0;
            border: none;
            width: 90%;
            max-width: 600px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.3);
        }
        
        .custom-modal-header {
            background-color: #1e6b4a;
            color: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .custom-modal-header h3 {
            margin: 0;
            color: #00ffff;
        }
        
        .custom-modal-close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .custom-modal-close:hover {
            color: #fff;
        }
        
        .custom-modal-body {
            padding: 20px;
            color: white;
        }
        
        .custom-modal-footer {
            background-color: #1e6b4a;
            padding: 15px 20px;
            border-radius: 0 0 10px 10px;
            text-align: right;
        }
        
        .custom-modal-btn {
            padding: 10px 20px;
            margin: 0 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .custom-modal-btn.primary {
            background-color: #4CAF50;
            color: white;
        }
        
        .custom-modal-btn.secondary {
            background-color: #666;
            color: white;
        }
        
        .custom-modal-btn:hover {
            opacity: 0.8;
        }
    </style>
</body>
</html>
