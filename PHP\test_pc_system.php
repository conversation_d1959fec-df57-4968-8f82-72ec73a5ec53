<?php
// Test PC Components System
session_start();
require_once 'config.php';

// Check if user is admin
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    die('Admin access required');
}

echo "<h1>Testing PC Components System</h1>";

// Test database connection
echo "<h2>1. Database Connection Test</h2>";
if ($link) {
    echo "<p style='color: green;'>✓ Database connection successful</p>";
} else {
    echo "<p style='color: red;'>✗ Database connection failed</p>";
    die();
}

// Test tables existence
echo "<h2>2. Tables Existence Test</h2>";
$tables = [
    'pc_component_categories',
    'pc_components', 
    'pc_prebuilt_configs',
    'pc_orders'
];

foreach ($tables as $table) {
    $result = mysqli_query($link, "SHOW TABLES LIKE '$table'");
    if (mysqli_num_rows($result) > 0) {
        echo "<p style='color: green;'>✓ Table '$table' exists</p>";
        
        // Show row count
        $countResult = mysqli_query($link, "SELECT COUNT(*) as count FROM $table");
        $count = mysqli_fetch_assoc($countResult)['count'];
        echo "<p style='color: blue;'>  → Contains $count rows</p>";
    } else {
        echo "<p style='color: red;'>✗ Table '$table' not found</p>";
    }
}

// Test API endpoints
echo "<h2>3. API Endpoints Test</h2>";

// Test get categories
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/pc_components_api.php?action=get_categories');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "<p style='color: green;'>✓ Categories API working - Found " . count($data['categories']) . " categories</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Categories API returned: " . $response . "</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Categories API failed with HTTP code: $httpCode</p>";
}

// Test get components
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/pc_components_api.php?action=get_components&active_only=0');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "<p style='color: green;'>✓ Components API working - Found " . count($data['components']) . " components</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Components API returned: " . $response . "</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Components API failed with HTTP code: $httpCode</p>";
}

// Test get prebuilt configs
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/pc_components_api.php?action=get_prebuilt_configs&active_only=0');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "<p style='color: green;'>✓ Pre-built Configs API working - Found " . count($data['configs']) . " configurations</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Pre-built Configs API returned: " . $response . "</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Pre-built Configs API failed with HTTP code: $httpCode</p>";
}

// Test file permissions
echo "<h2>4. File Permissions Test</h2>";
$files = [
    'pc_components_api.php',
    'pc_components_api_extended.php',
    'admin_pc_management.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "<p style='color: green;'>✓ File '$file' is readable</p>";
        } else {
            echo "<p style='color: red;'>✗ File '$file' is not readable</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ File '$file' not found</p>";
    }
}

echo "<h2>5. System Status Summary</h2>";
echo "<p><strong>System appears to be ready for use!</strong></p>";
echo "<p><a href='admin_pc_management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to PC Management</a></p>";
echo "<p><a href='admin.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Admin Panel</a></p>";

// Close connection
mysqli_close($link);
?>
