<?php
// This script is called via sendBeacon when a user closes the member page.
// With the new time-based online detection, we don't need to set last_seen to NULL.
// The system will automatically consider users offline after 5 minutes of inactivity.
// This script can remain for compatibility but doesn't need to do anything.

session_start();

// Log the offline event for debugging purposes (optional)
if (isset($_SESSION['loggedin']) && $_SESSION['loggedin'] === true && isset($_SESSION['username'])) {
    // We could log this event if needed, but no database update is required
    // The time-based online detection will handle offline status automatically
    error_log("User {$_SESSION['username']} closed member page at " . date('Y-m-d H:i:s'));
}

// No output is necessary for a beacon request.
exit;