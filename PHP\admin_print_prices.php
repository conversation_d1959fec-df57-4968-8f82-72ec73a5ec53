<?php
/**
 * Admin Print Service Prices Management
 */

session_start();
require_once 'config.php';
require_once 'functions.php';

// Check admin access
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || !$_SESSION["is_admin"]) {
    header("location: login.php");
    exit;
}

// Get current language
$lang = $_SESSION['language'] ?? 'en';
$translations = loadTranslations($lang);

function t($key) {
    global $translations;
    return $translations[$key] ?? $key;
}
?>

<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= t('site_title') ?> - Print Prices Management</title>
    <link rel="stylesheet" href="../CSS/main.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #a48f19;
            color: white;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 10px;
            background-color: rgb(5 195 182);
            border-radius: 10px;
            margin-top: 10px;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        
        .back-link {
            position: fixed;
            top: 10px;
            right: 10px;
            background-color: #00bcaa;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 5px 10px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .back-link:hover {
            background-color: #ffbf00ff;
            border-color: rgba(255, 255, 255, 0.3);
        }
        
        .page-header {
            text-align: center;
            color: #ffffff;
            margin-bottom: 30px;
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 5px 5px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background-color: rgb(253, 202, 0);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .btn-success {
            background-color: rgb(253, 202, 0);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .btn-warning {
            background-color: rgb(253, 202, 0);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .btn-secondary {
            background-color: #ffd000;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        
        .btn:hover {
            background-color: rgba(255, 255, 255, 0.5);
            border-color: rgba(255, 255, 255, 0.8);
        }
        
        .prices-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(255,255,255,0.1);
        }
        
        .prices-table th,
        .prices-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            color: white;
        }
        
        .prices-table th {
            background: rgba(253, 202, 0, 0.3);
            font-weight: bold;
        }
        
        .prices-table tr:hover {
            background: rgba(255,255,255,0.1);
        }
        
        /* Drag and Drop Styles */
        .prices-table tbody tr {
            cursor: move;
            transition: all 0.3s ease;
        }
        
        .prices-table tbody tr:hover {
            background: rgba(255,255,255,0.2);
            transform: scale(1.01);
        }
        
        .dragging {
            opacity: 0.5;
            transform: rotate(5deg);
            background: rgba(253, 202, 0, 0.3) !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        
        .drag-over {
            border-top: 3px solid rgb(253, 202, 0);
            background: rgba(253, 202, 0, 0.1) !important;
        }
        
        .drag-handle {
            color: rgba(255,255,255,0.6);
            cursor: grab;
            padding: 0 8px;
            user-select: none;
        }
        
        .drag-handle:hover {
            color: rgb(253, 202, 0);
        }
        
        .drag-handle:active {
            cursor: grabbing;
        }
        
        .sort-info {
            background: rgba(253, 202, 0, 0.1);
            border: 1px solid rgba(253, 202, 0, 0.3);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
            color: white;
            font-size: 14px;
        }
        
        .status-active {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-inactive {
            color: #dc3545;
            font-weight: bold;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }
        
        .modal-content {
            background-color: rgb(5 195 182);
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 800px;
            color: white;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: white;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #ffffff;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 95%;
            padding: 8px;
            border: 2px solid rgb(253, 202, 0);
            border-radius: 12px;
            background: rgb(255 194 0);
            color: #000;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .loading {
            text-align: center;
            color: #ffffff;
            padding: 20px;
        }
        
        .error {
            color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success {
            color: #28a745;
            background: rgba(40, 167, 69, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">&larr; Back to Admin Panel</a>
        
        <div class="page-header">
            <h1>🖨️ Print Service - Price Management</h1>
            <p>Manage pricing for print services, papers, and albums</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-success" onclick="openAddModal()">➕ Add New Service</button>
            <button class="btn btn-primary" onclick="loadPrices()">🔄 Refresh</button>
            <a href="admin_optimize_prices.php" class="btn btn-secondary">📸 Manage Optimize Prices</a>
        </div>
        
        <div id="message"></div>
        
        <div class="sort-info">
            <strong>💡 智慧排序：</strong> 直接拖拽服務項目的上下位置來自動調整排序順序。拖拽左側的 ⋮⋮ 圖標來移動項目。
        </div>
        
        <div id="pricesContainer">
            <div class="loading">Loading prices...</div>
        </div>
    </div>
    
    <!-- Edit/Add Price Modal -->
    <div id="priceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Edit Price</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <form id="priceForm">
                <input type="hidden" id="priceId" name="id">
                <input type="hidden" id="serviceCategory" name="service_category" value="print">
                
                <div class="form-group">
                    <label for="serviceType">Service Type:</label>
                    <select id="serviceType" name="service_type" required>
                        <option value="">Select Type</option>
                        <option value="paper_32lb_black">32lb Paper - Black</option>
                        <option value="paper_32lb_color">32lb Paper - Color</option>
                        <option value="paper_110lb_black">110lb Paper - Black</option>
                        <option value="paper_110lb_color">110lb Paper - Color</option>
                        <option value="photo_45lb_black">45lb Photo Paper - Black</option>
                        <option value="photo_45lb_color">45lb Photo Paper - Color</option>
                        <option value="photo_70lb_black">70lb Photo Paper - Black</option>
                        <option value="photo_70lb_color">70lb Photo Paper - Color</option>
                        <option value="laminating_letter">Letter Laminating</option>
                        <option value="photo_4x6">4x6 Photo</option>
                        <option value="laminating_4x6">4x6 Laminating</option>
                        <option value="album_basic">Basic Album</option>
                        <option value="album_premium_52">Premium Album 52</option>
                        <option value="album_premium_300">Premium Album 300</option>
                        <option value="album_premium_600">Premium Album 600</option>
                        <option value="custom">Custom Type</option>
                    </select>
                </div>
                
                <div id="customTypeGroup" class="form-group" style="display: none;">
                    <label for="customServiceType">Custom Service Type:</label>
                    <input type="text" id="customServiceType" name="custom_service_type">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="itemName">Item Name:</label>
                        <input type="text" id="itemName" name="item_name" required>
                    </div>
                    <div class="form-group">
                        <label for="basePrice">Base Price ($):</label>
                        <input type="number" id="basePrice" name="base_price" step="0.01" min="0" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="itemNameEn">English Name:</label>
                        <input type="text" id="itemNameEn" name="item_name_en">
                    </div>
                    <div class="form-group">
                        <label for="itemNameZh">Chinese Name:</label>
                        <input type="text" id="itemNameZh" name="item_name_zh">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="unit">Unit:</label>
                        <select id="unit" name="unit">
                            <option value="each">Each</option>
                            <option value="per page">Per Page</option>
                            <option value="per sheet">Per Sheet</option>
                            <option value="per item">Per Item</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="sortOrder">Sort Order:</label>
                        <input type="number" id="sortOrder" name="sort_order" min="0" value="0">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="description">Description:</label>
                    <textarea id="description" name="description"></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="descriptionEn">English Description:</label>
                        <textarea id="descriptionEn" name="description_en"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="descriptionZh">Chinese Description:</label>
                        <textarea id="descriptionZh" name="description_zh"></textarea>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="isActive" name="is_active" checked>
                        Active
                    </label>
                </div>
                
                <div class="controls">
                    <button type="submit" class="btn btn-success">💾 Save</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">❌ Cancel</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        let currentEditId = null;
        
        // Load prices on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadPrices();
            
            // Handle service type change
            document.getElementById('serviceType').addEventListener('change', function() {
                const customGroup = document.getElementById('customTypeGroup');
                if (this.value === 'custom') {
                    customGroup.style.display = 'block';
                } else {
                    customGroup.style.display = 'none';
                }
            });
        });
        
        // Load all print prices
        function loadPrices() {
            fetch('service_prices_api.php?action=get_prices&category=print&active_only=false')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayPrices(data.prices);
                    } else {
                        showMessage('Error loading prices: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showMessage('Error: ' + error.message, 'error');
                });
        }
        
        // Display prices in table
        function displayPrices(prices) {
            let html = `
                <table class="prices-table">
                    <thead>
                        <tr>
                            <th width="40">拖拽</th>
                            <th>Service Type</th>
                            <th>Item Name</th>
                            <th>Price</th>
                            <th>Unit</th>
                            <th>Status</th>
                            <th>Sort Order</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="sortableTable">
            `;
            
            prices.forEach(price => {
                html += `
                    <tr data-id="${price.id}" data-sort-order="${price.sort_order}" draggable="true">
                        <td class="drag-handle">⋮⋮</td>
                        <td>${price.service_type}</td>
                        <td>${price.item_name}</td>
                        <td>$${parseFloat(price.base_price).toFixed(2)}</td>
                        <td>${price.unit}</td>
                        <td class="${price.is_active == 1 ? 'status-active' : 'status-inactive'}">
                            ${price.is_active == 1 ? 'Active' : 'Inactive'}
                        </td>
                        <td>${price.sort_order}</td>
                        <td>
                            <button class="btn btn-primary" onclick="editPrice(${price.id})">✏️ Edit</button>
                            <button class="btn btn-warning" onclick="toggleActive(${price.id})">
                                ${price.is_active == 1 ? '🚫 Deactivate' : '✅ Activate'}
                            </button>
                            <button class="btn btn-danger" onclick="deletePrice(${price.id})">🗑️ Delete</button>
                        </td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            document.getElementById('pricesContainer').innerHTML = html;
            
            // Initialize drag and drop functionality
            initializeDragAndDrop();
        }
        
        // Open add modal
        function openAddModal() {
            currentEditId = null;
            document.getElementById('modalTitle').textContent = 'Add New Service';
            document.getElementById('priceForm').reset();
            document.getElementById('priceId').value = '';
            document.getElementById('serviceCategory').value = 'print';
            document.getElementById('isActive').checked = true;
            document.getElementById('customTypeGroup').style.display = 'none';
            document.getElementById('priceModal').style.display = 'block';
        }
        
        // Edit price
        function editPrice(id) {
            currentEditId = id;
            document.getElementById('modalTitle').textContent = 'Edit Service Price';
            
            fetch(`service_prices_api.php?action=get_price&id=${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const price = data.price;
                        document.getElementById('priceId').value = price.id;
                        document.getElementById('serviceType').value = price.service_type;
                        document.getElementById('itemName').value = price.item_name;
                        document.getElementById('itemNameEn').value = price.item_name_en;
                        document.getElementById('itemNameZh').value = price.item_name_zh;
                        document.getElementById('basePrice').value = price.base_price;
                        document.getElementById('unit').value = price.unit;
                        document.getElementById('description').value = price.description || '';
                        document.getElementById('descriptionEn').value = price.description_en || '';
                        document.getElementById('descriptionZh').value = price.description_zh || '';
                        document.getElementById('isActive').checked = price.is_active == 1;
                        document.getElementById('sortOrder').value = price.sort_order;
                        
                        // Handle custom service type
                        const serviceTypeSelect = document.getElementById('serviceType');
                        const customGroup = document.getElementById('customTypeGroup');
                        if (!Array.from(serviceTypeSelect.options).some(option => option.value === price.service_type)) {
                            serviceTypeSelect.value = 'custom';
                            document.getElementById('customServiceType').value = price.service_type;
                            customGroup.style.display = 'block';
                        } else {
                            customGroup.style.display = 'none';
                        }
                        
                        document.getElementById('priceModal').style.display = 'block';
                    } else {
                        showMessage('Error loading price: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showMessage('Error: ' + error.message, 'error');
                });
        }
        
        // Close modal
        function closeModal() {
            document.getElementById('priceModal').style.display = 'none';
        }
        
        // Handle form submission
        document.getElementById('priceForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const action = currentEditId ? 'update_price' : 'add_price';
            formData.append('action', action);
            
            // Handle custom service type
            const serviceType = document.getElementById('serviceType').value;
            if (serviceType === 'custom') {
                const customType = document.getElementById('customServiceType').value;
                if (customType) {
                    formData.set('service_type', customType);
                }
            }
            
            fetch('service_prices_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    closeModal();
                    loadPrices();
                } else {
                    showMessage('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('Error: ' + error.message, 'error');
            });
        });
        
        // Toggle active status
        function toggleActive(id) {
            const formData = new FormData();
            formData.append('action', 'toggle_active');
            formData.append('id', id);
            
            fetch('service_prices_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    loadPrices();
                } else {
                    showMessage('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('Error: ' + error.message, 'error');
            });
        }
        
        // Delete price
        function deletePrice(id) {
            if (!confirm('Are you sure you want to delete this price? This action cannot be undone.')) {
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'delete_price');
            formData.append('id', id);
            
            fetch('service_prices_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    loadPrices();
                } else {
                    showMessage('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('Error: ' + error.message, 'error');
            });
        }
        
        // Show message
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 5000);
        }
        
        // Drag and Drop functionality
        let draggedElement = null;
        let originalSortOrder = [];
        
        function initializeDragAndDrop() {
            const tbody = document.getElementById('sortableTable');
            if (!tbody) return;
            
            const rows = tbody.querySelectorAll('tr[data-id]');
            
            rows.forEach(row => {
                // Store original sort order
                originalSortOrder.push({
                    id: row.dataset.id,
                    sortOrder: row.dataset.sortOrder
                });
                
                // Add drag event listeners
                row.addEventListener('dragstart', handleDragStart);
                row.addEventListener('dragend', handleDragEnd);
                row.addEventListener('dragover', handleDragOver);
                row.addEventListener('drop', handleDrop);
                row.addEventListener('dragenter', handleDragEnter);
                row.addEventListener('dragleave', handleDragLeave);
            });
        }
        
        function handleDragStart(e) {
            draggedElement = this;
            this.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', this.outerHTML);
        }
        
        function handleDragEnd(e) {
            this.classList.remove('dragging');
            
            // Remove drag-over class from all rows
            document.querySelectorAll('.drag-over').forEach(row => {
                row.classList.remove('drag-over');
            });
            
            draggedElement = null;
        }
        
        function handleDragOver(e) {
            if (e.preventDefault) {
                e.preventDefault();
            }
            e.dataTransfer.dropEffect = 'move';
            return false;
        }
        
        function handleDragEnter(e) {
            if (this !== draggedElement) {
                this.classList.add('drag-over');
            }
        }
        
        function handleDragLeave(e) {
            this.classList.remove('drag-over');
        }
        
        function handleDrop(e) {
            if (e.stopPropagation) {
                e.stopPropagation();
            }
            
            if (draggedElement !== this) {
                // Get the current position of dragged element and drop target
                const draggedIndex = Array.from(this.parentNode.children).indexOf(draggedElement);
                const dropIndex = Array.from(this.parentNode.children).indexOf(this);
                
                // Move the dragged element to the new position
                if (draggedIndex < dropIndex) {
                    this.parentNode.insertBefore(draggedElement, this.nextSibling);
                } else {
                    this.parentNode.insertBefore(draggedElement, this);
                }
                
                // Update sort orders
                updateSortOrders();
            }
            
            this.classList.remove('drag-over');
            return false;
        }
        
        function updateSortOrders() {
            const tbody = document.getElementById('sortableTable');
            const rows = tbody.querySelectorAll('tr[data-id]');
            const updates = [];
            
            rows.forEach((row, index) => {
                const id = row.dataset.id;
                const newSortOrder = index + 1;
                
                // Update the visual display
                const sortOrderCell = row.querySelector('td:nth-child(7)');
                if (sortOrderCell) {
                    sortOrderCell.textContent = newSortOrder;
                }
                
                // Add to updates array
                updates.push({
                    id: id,
                    sort_order: newSortOrder
                });
            });
            
            // Send updates to server
            if (updates.length > 0) {
                saveSortOrders(updates);
            }
        }
        
        function saveSortOrders(updates) {
            const formData = new FormData();
            formData.append('action', 'update_sort_orders');
            formData.append('updates', JSON.stringify(updates));
            
            fetch('service_prices_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('排序順序已更新！', 'success');
                } else {
                    showMessage('更新排序失敗: ' + data.message, 'error');
                    // Reload prices to restore original order
                    loadPrices();
                }
            })
            .catch(error => {
                showMessage('更新排序時發生錯誤: ' + error.message, 'error');
                // Reload prices to restore original order
                loadPrices();
            });
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('priceModal');
            if (event.target == modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
