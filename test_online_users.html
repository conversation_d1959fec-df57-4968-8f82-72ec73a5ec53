<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Online Users</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .online-count {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .debug-info {
            background-color: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Online Users Test</h1>
        <p>This page tests the online user detection system.</p>
        
        <div id="online-status" class="status online-count">
            Loading online users...
        </div>
        
        <div id="debug-info" class="status debug-info">
            Loading debug information...
        </div>
        
        <button onclick="refreshData()">Refresh Data</button>
        <button onclick="toggleAutoRefresh()">Toggle Auto Refresh</button>
        <button onclick="window.open('PHP/member.php', '_blank')">Open Member Page</button>
        
        <div style="margin-top: 20px;">
            <h3>Instructions:</h3>
            <ol>
                <li>Open the member page in another tab/window</li>
                <li>Login with your account</li>
                <li>Watch the online count update</li>
                <li>Close the member page and wait 5+ minutes</li>
                <li>The count should decrease</li>
            </ol>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefresh = false;

        function updateOnlineUsers() {
            fetch('PHP/online_users.php')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('online-status');
                    if (data.error) {
                        statusDiv.className = 'status error';
                        statusDiv.textContent = `Error: ${data.error}`;
                    } else {
                        statusDiv.className = 'status online-count';
                        statusDiv.textContent = `Online Users: ${data.online_users}`;
                    }
                })
                .catch(error => {
                    const statusDiv = document.getElementById('online-status');
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `Fetch Error: ${error.message}`;
                });
        }

        function updateDebugInfo() {
            fetch('PHP/debug_online_users.php')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('debug-info').textContent = data;
                })
                .catch(error => {
                    document.getElementById('debug-info').textContent = `Debug Error: ${error.message}`;
                });
        }

        function refreshData() {
            updateOnlineUsers();
            updateDebugInfo();
        }

        function toggleAutoRefresh() {
            if (isAutoRefresh) {
                clearInterval(autoRefreshInterval);
                isAutoRefresh = false;
                document.querySelector('button[onclick="toggleAutoRefresh()"]').textContent = 'Start Auto Refresh';
            } else {
                autoRefreshInterval = setInterval(refreshData, 5000); // Every 5 seconds
                isAutoRefresh = true;
                document.querySelector('button[onclick="toggleAutoRefresh()"]').textContent = 'Stop Auto Refresh';
            }
        }

        // Initial load
        refreshData();
        
        // Start auto refresh by default
        toggleAutoRefresh();
    </script>
</body>
</html>
